/* 画布容器样式 */
.canvas-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: #fafafa;
    position: relative;
    overflow: hidden;
}

.canvas-wrapper {
    flex: 1;
    position: relative;
    overflow: hidden;
    background: white;
    border: 1px solid #e0e0e0;
    margin: 1rem;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
}

#officeCanvas {
    display: block;
    cursor: crosshair;
    background: white;
    border-radius: 8px;
}

/* 画布控制按钮 */
.canvas-controls {
    position: absolute;
    bottom: 2rem;
    right: 2rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: rgba(255, 255, 255, 0.95);
    padding: 0.75rem;
    border-radius: 25px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.control-btn {
    width: 40px;
    height: 40px;
    border: none;
    border-radius: 50%;
    background: #667eea;
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.control-btn:hover {
    background: #5a67d8;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.control-btn:active {
    transform: translateY(0);
}

#zoomLevel {
    font-size: 0.9rem;
    font-weight: 600;
    color: #555;
    min-width: 50px;
    text-align: center;
    background: rgba(255, 255, 255, 0.8);
    padding: 0.5rem;
    border-radius: 15px;
    border: 1px solid rgba(0,0,0,0.1);
}

/* 画布网格背景 */
.canvas-wrapper::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: 
        linear-gradient(rgba(0,0,0,0.05) 1px, transparent 1px),
        linear-gradient(90deg, rgba(0,0,0,0.05) 1px, transparent 1px);
    background-size: 20px 20px;
    pointer-events: none;
    z-index: 0;
}

/* 选中对象的高亮效果 */
.canvas-object-selected {
    box-shadow: 0 0 0 2px #667eea !important;
}

/* 工具提示 */
.canvas-tooltip {
    position: absolute;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 0.5rem 0.75rem;
    border-radius: 6px;
    font-size: 0.8rem;
    pointer-events: none;
    z-index: 1000;
    white-space: nowrap;
    transform: translate(-50%, -100%);
    margin-top: -8px;
}

.canvas-tooltip::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 4px solid transparent;
    border-top-color: rgba(0, 0, 0, 0.8);
}

/* 绘制模式下的光标 */
.canvas-wrapper.drawing-mode {
    cursor: crosshair;
}

.canvas-wrapper.selection-mode {
    cursor: default;
}

.canvas-wrapper.pan-mode {
    cursor: grab;
}

.canvas-wrapper.pan-mode:active {
    cursor: grabbing;
}

/* 对象类型特定样式 */
.office-seat {
    fill: #e3f2fd;
    stroke: #1976d2;
    stroke-width: 2;
}

.office-seat.occupied {
    fill: #ffebee;
    stroke: #d32f2f;
}

.office-meeting-room {
    fill: #f3e5f5;
    stroke: #7b1fa2;
    stroke-width: 2;
}

.office-reception {
    fill: #e8f5e8;
    stroke: #388e3c;
    stroke-width: 2;
}

.office-wall {
    fill: #f5f5f5;
    stroke: #424242;
    stroke-width: 3;
}

.office-door {
    fill: #fff3e0;
    stroke: #f57c00;
    stroke-width: 2;
}

/* 标签文本样式 */
.office-label {
    font-family: 'Segoe UI', sans-serif;
    font-size: 12px;
    fill: #333;
    text-anchor: middle;
    dominant-baseline: central;
    pointer-events: none;
}

/* 搜索高亮效果 */
.search-highlight {
    animation: searchPulse 2s ease-in-out infinite;
}

@keyframes searchPulse {
    0%, 100% {
        box-shadow: 0 0 0 0 rgba(255, 193, 7, 0.7);
    }
    50% {
        box-shadow: 0 0 0 10px rgba(255, 193, 7, 0);
    }
}

/* 拖拽时的样式 */
.fabric-object-dragging {
    opacity: 0.7;
}

/* 选择框样式 */
.fabric-selection {
    border: 2px dashed #667eea !important;
    background: rgba(102, 126, 234, 0.1) !important;
}

/* 控制点样式 */
.fabric-corner {
    background: #667eea !important;
    border: 2px solid white !important;
    border-radius: 50% !important;
    width: 12px !important;
    height: 12px !important;
}

/* 旋转控制点 */
.fabric-rotation-point {
    background: #ff9800 !important;
    border: 2px solid white !important;
    border-radius: 50% !important;
}

/* 加载状态 */
.canvas-loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
    color: #666;
}

.canvas-loading .spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 右键菜单 */
.context-menu {
    position: absolute;
    background: white;
    border: 1px solid #ddd;
    border-radius: 6px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    z-index: 1000;
    min-width: 150px;
    overflow: hidden;
}

.context-menu-item {
    padding: 0.75rem 1rem;
    cursor: pointer;
    border-bottom: 1px solid #f0f0f0;
    transition: background-color 0.2s ease;
}

.context-menu-item:hover {
    background-color: #f8f9ff;
}

.context-menu-item:last-child {
    border-bottom: none;
}

.context-menu-item.disabled {
    color: #ccc;
    cursor: not-allowed;
}

.context-menu-item.disabled:hover {
    background-color: transparent;
}

/* 小屏幕适配 */
@media (max-width: 768px) {
    .canvas-wrapper {
        margin: 0.5rem;
    }
    
    .canvas-controls {
        bottom: 1rem;
        right: 1rem;
        padding: 0.5rem;
    }
    
    .control-btn {
        width: 35px;
        height: 35px;
        font-size: 0.8rem;
    }
    
    #zoomLevel {
        font-size: 0.8rem;
        padding: 0.4rem;
        min-width: 45px;
    }
}
