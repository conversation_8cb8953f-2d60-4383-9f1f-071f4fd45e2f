/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f5f5f5;
    color: #333;
}

.app-container {
    height: 100vh;
    display: flex;
    flex-direction: column;
}

/* 顶部工具栏 */
.toolbar {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.toolbar h1 {
    font-size: 1.5rem;
    font-weight: 600;
}

.toolbar-section {
    display: flex;
    align-items: center;
    gap: 1rem;
}

/* 按钮样式 */
.btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-primary {
    background-color: #4CAF50;
    color: white;
}

.btn-primary:hover {
    background-color: #45a049;
    transform: translateY(-1px);
}

.btn-secondary {
    background-color: #2196F3;
    color: white;
}

.btn-secondary:hover {
    background-color: #1976D2;
    transform: translateY(-1px);
}

.btn-danger {
    background-color: #f44336;
    color: white;
}

.btn-danger:hover {
    background-color: #d32f2f;
    transform: translateY(-1px);
}

/* 主内容区域 */
.main-content {
    flex: 1;
    display: flex;
    overflow: hidden;
}

/* 侧边面板 */
.left-panel, .right-panel {
    width: 280px;
    background: white;
    border-right: 1px solid #e0e0e0;
    overflow-y: auto;
    box-shadow: 2px 0 10px rgba(0,0,0,0.05);
}

.right-panel {
    border-right: none;
    border-left: 1px solid #e0e0e0;
    box-shadow: -2px 0 10px rgba(0,0,0,0.05);
}

.panel-section {
    padding: 1.5rem;
    border-bottom: 1px solid #f0f0f0;
}

.panel-section h3 {
    margin-bottom: 1rem;
    color: #555;
    font-size: 1.1rem;
    font-weight: 600;
}

/* 工具网格 */
.tool-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 0.75rem;
}

.tool-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 1rem;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    background: white;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
}

.tool-btn:hover {
    border-color: #667eea;
    background-color: #f8f9ff;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
}

.tool-btn.active {
    border-color: #667eea;
    background-color: #667eea;
    color: white;
}

.tool-icon {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
}

.tool-label {
    font-size: 0.8rem;
    font-weight: 500;
}

/* 搜索容器 */
.search-container {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.search-container input {
    padding: 0.75rem;
    border: 2px solid #e0e0e0;
    border-radius: 6px;
    font-size: 0.9rem;
    transition: border-color 0.3s ease;
}

.search-container input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.search-results {
    margin-top: 1rem;
    max-height: 200px;
    overflow-y: auto;
}

.search-result-item {
    padding: 0.75rem;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    margin-bottom: 0.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    background: white;
}

.search-result-item:hover {
    background-color: #f8f9ff;
    border-color: #667eea;
    transform: translateX(4px);
}

.search-result-item .result-title {
    font-weight: 600;
    color: #333;
    margin-bottom: 0.25rem;
}

.search-result-item .result-info {
    font-size: 0.8rem;
    color: #666;
}

/* 属性面板 */
.properties-panel {
    min-height: 200px;
}

.no-selection {
    color: #999;
    font-style: italic;
    text-align: center;
    padding: 2rem;
}

.property-group {
    margin-bottom: 1.5rem;
}

.property-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #555;
}

.property-group input,
.property-group select,
.property-group textarea {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 0.9rem;
}

.property-group textarea {
    resize: vertical;
    min-height: 60px;
}

/* 图层管理 */
.layer-list {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.layer-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    background: white;
}

.layer-item.active {
    border-color: #667eea;
    background-color: #f8f9ff;
}

.layer-name {
    font-weight: 500;
}

.layer-toggle {
    background: none;
    border: none;
    cursor: pointer;
    font-size: 1rem;
    padding: 0.25rem;
    border-radius: 4px;
    transition: background-color 0.3s ease;
}

.layer-toggle:hover {
    background-color: #f0f0f0;
}

/* 模态对话框 */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    backdrop-filter: blur(4px);
}

.modal-content {
    background-color: white;
    margin: 5% auto;
    padding: 2rem;
    border-radius: 12px;
    width: 90%;
    max-width: 500px;
    box-shadow: 0 20px 60px rgba(0,0,0,0.3);
    animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.close {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    line-height: 1;
}

.close:hover {
    color: #333;
}

/* 统计信息样式 */
.statistics-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
    margin: 1rem 0;
}

.stat-item {
    text-align: center;
    padding: 1rem;
    background: #f8f9ff;
    border-radius: 8px;
    border: 1px solid #e0e0e0;
}

.stat-number {
    font-size: 2rem;
    font-weight: bold;
    color: #667eea;
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 0.9rem;
    color: #666;
}

/* 帮助内容样式 */
.help-content {
    max-height: 400px;
    overflow-y: auto;
}

.help-section {
    margin-bottom: 1.5rem;
}

.help-section h4 {
    color: #667eea;
    margin-bottom: 0.75rem;
    font-size: 1.1rem;
}

.help-section ul {
    list-style: none;
    padding-left: 0;
}

.help-section li {
    padding: 0.5rem 0;
    border-bottom: 1px solid #f0f0f0;
}

.help-section li:last-child {
    border-bottom: none;
}

.help-section strong {
    color: #333;
    background: #f5f5f5;
    padding: 0.2rem 0.4rem;
    border-radius: 3px;
    font-size: 0.85rem;
}

/* 表单样式 */
.form-group {
    margin-bottom: 1rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #555;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #e0e0e0;
    border-radius: 6px;
    font-size: 0.9rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* 详情显示样式 */
.employee-details,
.room-details {
    margin: 1rem 0;
}

.detail-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid #f0f0f0;
}

.detail-row:last-child {
    border-bottom: none;
}

.detail-row label {
    font-weight: 600;
    color: #555;
    min-width: 80px;
}

.detail-row span {
    color: #333;
    text-align: right;
}

/* 搜索结果样式增强 */
.search-section {
    margin-bottom: 1rem;
}

.search-section h4 {
    color: #667eea;
    margin-bottom: 0.5rem;
    font-size: 1rem;
    padding-bottom: 0.25rem;
    border-bottom: 2px solid #667eea;
}

.search-no-results {
    text-align: center;
    padding: 2rem;
    color: #999;
}

.search-no-results p {
    font-size: 1.1rem;
    margin-bottom: 0.5rem;
}

.search-no-results small {
    font-size: 0.9rem;
}

/* 高亮搜索结果 */
mark {
    background-color: #fff3cd;
    color: #856404;
    padding: 0.1rem 0.2rem;
    border-radius: 3px;
}

/* 模态对话框动作按钮 */
.modal-actions {
    margin-top: 1.5rem;
    display: flex;
    gap: 0.75rem;
    justify-content: flex-end;
}

/* 标签页样式 */
.search-tabs {
    display: flex;
    margin-bottom: 1rem;
    border-bottom: 2px solid #f0f0f0;
}

.tab-btn {
    padding: 0.75rem 1.5rem;
    border: none;
    background: none;
    cursor: pointer;
    font-size: 0.9rem;
    color: #666;
    border-bottom: 2px solid transparent;
    transition: all 0.3s ease;
}

.tab-btn.active {
    color: #667eea;
    border-bottom-color: #667eea;
    background: rgba(102, 126, 234, 0.05);
}

.tab-btn:hover {
    color: #667eea;
    background: rgba(102, 126, 234, 0.05);
}

.search-tab-content {
    display: none;
}

.search-tab-content.active {
    display: block;
}

/* 通知样式增强 */
.notification {
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    backdrop-filter: blur(10px);
}

/* 响应式设计 */
@media (max-width: 1024px) {
    .left-panel, .right-panel {
        width: 240px;
    }

    .tool-grid {
        grid-template-columns: 1fr;
    }

    .statistics-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .main-content {
        flex-direction: column;
    }

    .left-panel, .right-panel {
        width: 100%;
        height: auto;
        max-height: 200px;
    }

    .toolbar {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .toolbar-section {
        justify-content: center;
    }

    .statistics-grid {
        grid-template-columns: 1fr;
    }

    .modal-content {
        width: 95%;
        margin: 2% auto;
        padding: 1rem;
    }

    .detail-row {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.25rem;
    }

    .detail-row span {
        text-align: left;
    }
}
