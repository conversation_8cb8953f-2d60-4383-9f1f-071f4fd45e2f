<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>办公区域管理系统</title>
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/canvas.css">
</head>
<body>
    <div class="app-container">
        <!-- 顶部工具栏 -->
        <header class="toolbar">
            <div class="toolbar-section">
                <h1>办公区域管理系统</h1>
            </div>
            <div class="toolbar-section">
                <button id="saveBtn" class="btn btn-primary">💾 保存布局</button>
                <button id="loadBtn" class="btn btn-secondary">📁 加载布局</button>
                <button id="clearBtn" class="btn btn-danger">🗑️ 清空画布</button>
                <button onclick="showStatistics()" class="btn btn-secondary">📊 统计信息</button>
                <button onclick="showHelp()" class="btn btn-secondary">❓ 帮助</button>
            </div>
        </header>

        <div class="main-content">
            <!-- 左侧工具面板 -->
            <aside class="left-panel">
                <div class="panel-section">
                    <h3>绘图工具</h3>
                    <div class="tool-grid">
                        <button class="tool-btn" data-tool="seat" title="座位">
                            <span class="tool-icon">💺</span>
                            <span class="tool-label">座位</span>
                        </button>
                        <button class="tool-btn" data-tool="meeting-room" title="会议室">
                            <span class="tool-icon">🏢</span>
                            <span class="tool-label">会议室</span>
                        </button>
                        <button class="tool-btn" data-tool="reception" title="前台">
                            <span class="tool-icon">🏪</span>
                            <span class="tool-label">前台</span>
                        </button>
                        <button class="tool-btn" data-tool="wall" title="墙体">
                            <span class="tool-icon">🧱</span>
                            <span class="tool-label">墙体</span>
                        </button>
                        <button class="tool-btn" data-tool="door" title="门">
                            <span class="tool-icon">🚪</span>
                            <span class="tool-label">门</span>
                        </button>
                        <button class="tool-btn" data-tool="text" title="文本标记">
                            <span class="tool-icon">📝</span>
                            <span class="tool-label">文本</span>
                        </button>
                    </div>
                </div>

                <div class="panel-section">
                    <h3>搜索功能</h3>
                    <div class="search-container">
                        <input type="text" id="searchInput" placeholder="输入员工姓名/工号或会议室名称">
                        <button id="searchBtn" class="btn btn-primary">搜索</button>
                    </div>
                    <div id="searchResults" class="search-results"></div>
                </div>
            </aside>

            <!-- 中央画布区域 -->
            <main class="canvas-container">
                <div class="canvas-wrapper">
                    <canvas id="officeCanvas" width="1200" height="800"></canvas>
                </div>
                <div class="canvas-controls">
                    <button id="zoomInBtn" class="control-btn">🔍+</button>
                    <button id="zoomOutBtn" class="control-btn">🔍-</button>
                    <button id="resetZoomBtn" class="control-btn">重置</button>
                    <span id="zoomLevel">100%</span>
                </div>
            </main>

            <!-- 右侧属性面板 -->
            <aside class="right-panel">
                <div class="panel-section">
                    <h3>属性设置</h3>
                    <div id="propertiesPanel" class="properties-panel">
                        <p class="no-selection">请选择一个对象来编辑属性</p>
                    </div>
                </div>

                <div class="panel-section">
                    <h3>图层管理</h3>
                    <div class="layer-list">
                        <div class="layer-item active">
                            <span class="layer-name">主图层</span>
                            <button class="layer-toggle">👁</button>
                        </div>
                    </div>
                </div>
            </aside>
        </div>
    </div>

    <!-- 模态对话框 -->
    <div id="modal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <div id="modalBody"></div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="lib/fabric.min.js"></script>
    <script src="js/data-manager.js"></script>
    <script src="js/canvas-manager.js"></script>
    <script src="js/search.js"></script>
    <script src="js/main.js"></script>
</body>
</html>
