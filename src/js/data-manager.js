/**
 * 数据管理器 - 负责数据的存储、加载和管理
 */
class DataManager {
    constructor() {
        this.storageKey = 'office-layout-data';
        this.employees = [];
        this.meetingRooms = [];
        this.officeObjects = [];
    }

    /**
     * 保存办公室布局数据到本地存储
     */
    saveLayout(canvasData) {
        try {
            const data = {
                canvasData: canvasData,
                employees: this.employees,
                meetingRooms: this.meetingRooms,
                officeObjects: this.officeObjects,
                timestamp: new Date().toISOString()
            };
            
            localStorage.setItem(this.storageKey, JSON.stringify(data));
            this.showNotification('布局保存成功！', 'success');
            return true;
        } catch (error) {
            console.error('保存布局失败:', error);
            this.showNotification('保存失败，请重试', 'error');
            return false;
        }
    }

    /**
     * 从本地存储加载办公室布局数据
     */
    loadLayout() {
        try {
            const data = localStorage.getItem(this.storageKey);
            if (!data) {
                this.showNotification('没有找到保存的布局', 'info');
                return null;
            }

            const parsedData = JSON.parse(data);
            this.employees = parsedData.employees || [];
            this.meetingRooms = parsedData.meetingRooms || [];
            this.officeObjects = parsedData.officeObjects || [];
            
            this.showNotification('布局加载成功！', 'success');
            return parsedData.canvasData;
        } catch (error) {
            console.error('加载布局失败:', error);
            this.showNotification('加载失败，请重试', 'error');
            return null;
        }
    }

    /**
     * 添加员工信息
     */
    addEmployee(employeeData) {
        const employee = {
            id: this.generateId(),
            name: employeeData.name,
            employeeId: employeeData.employeeId,
            department: employeeData.department || '',
            position: employeeData.position || '',
            seatId: employeeData.seatId || null,
            phone: employeeData.phone || '',
            email: employeeData.email || ''
        };
        
        this.employees.push(employee);
        return employee;
    }

    /**
     * 更新员工信息
     */
    updateEmployee(employeeId, updateData) {
        const index = this.employees.findIndex(emp => emp.id === employeeId);
        if (index !== -1) {
            this.employees[index] = { ...this.employees[index], ...updateData };
            return this.employees[index];
        }
        return null;
    }

    /**
     * 删除员工
     */
    removeEmployee(employeeId) {
        const index = this.employees.findIndex(emp => emp.id === employeeId);
        if (index !== -1) {
            this.employees.splice(index, 1);
            return true;
        }
        return false;
    }

    /**
     * 添加会议室信息
     */
    addMeetingRoom(roomData) {
        const room = {
            id: this.generateId(),
            name: roomData.name,
            capacity: roomData.capacity || 0,
            equipment: roomData.equipment || [],
            location: roomData.location || '',
            objectId: roomData.objectId || null
        };
        
        this.meetingRooms.push(room);
        return room;
    }

    /**
     * 更新会议室信息
     */
    updateMeetingRoom(roomId, updateData) {
        const index = this.meetingRooms.findIndex(room => room.id === roomId);
        if (index !== -1) {
            this.meetingRooms[index] = { ...this.meetingRooms[index], ...updateData };
            return this.meetingRooms[index];
        }
        return null;
    }

    /**
     * 删除会议室
     */
    removeMeetingRoom(roomId) {
        const index = this.meetingRooms.findIndex(room => room.id === roomId);
        if (index !== -1) {
            this.meetingRooms.splice(index, 1);
            return true;
        }
        return false;
    }

    /**
     * 搜索员工
     */
    searchEmployees(query) {
        if (!query) return [];
        
        const lowerQuery = query.toLowerCase();
        return this.employees.filter(emp => 
            emp.name.toLowerCase().includes(lowerQuery) ||
            emp.employeeId.toLowerCase().includes(lowerQuery) ||
            emp.department.toLowerCase().includes(lowerQuery)
        );
    }

    /**
     * 搜索会议室
     */
    searchMeetingRooms(query) {
        if (!query) return [];
        
        const lowerQuery = query.toLowerCase();
        return this.meetingRooms.filter(room => 
            room.name.toLowerCase().includes(lowerQuery) ||
            room.location.toLowerCase().includes(lowerQuery)
        );
    }

    /**
     * 根据座位ID查找员工
     */
    getEmployeeBySeatId(seatId) {
        return this.employees.find(emp => emp.seatId === seatId);
    }

    /**
     * 根据对象ID查找会议室
     */
    getMeetingRoomByObjectId(objectId) {
        return this.meetingRooms.find(room => room.objectId === objectId);
    }

    /**
     * 生成唯一ID
     */
    generateId() {
        return 'id_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    /**
     * 显示通知消息
     */
    showNotification(message, type = 'info') {
        // 创建通知元素
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.textContent = message;
        
        // 添加样式
        Object.assign(notification.style, {
            position: 'fixed',
            top: '20px',
            right: '20px',
            padding: '12px 20px',
            borderRadius: '6px',
            color: 'white',
            fontWeight: '500',
            zIndex: '10000',
            opacity: '0',
            transform: 'translateY(-20px)',
            transition: 'all 0.3s ease'
        });

        // 根据类型设置背景色
        const colors = {
            success: '#4CAF50',
            error: '#f44336',
            warning: '#ff9800',
            info: '#2196F3'
        };
        notification.style.backgroundColor = colors[type] || colors.info;

        // 添加到页面
        document.body.appendChild(notification);

        // 显示动画
        setTimeout(() => {
            notification.style.opacity = '1';
            notification.style.transform = 'translateY(0)';
        }, 100);

        // 自动移除
        setTimeout(() => {
            notification.style.opacity = '0';
            notification.style.transform = 'translateY(-20px)';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 3000);
    }

    /**
     * 导出数据为JSON文件
     */
    exportData() {
        try {
            const data = {
                employees: this.employees,
                meetingRooms: this.meetingRooms,
                officeObjects: this.officeObjects,
                exportTime: new Date().toISOString()
            };

            const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            
            const a = document.createElement('a');
            a.href = url;
            a.download = `office-data-${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            
            this.showNotification('数据导出成功！', 'success');
        } catch (error) {
            console.error('导出数据失败:', error);
            this.showNotification('导出失败，请重试', 'error');
        }
    }

    /**
     * 从JSON文件导入数据
     */
    importData(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (e) => {
                try {
                    const data = JSON.parse(e.target.result);
                    this.employees = data.employees || [];
                    this.meetingRooms = data.meetingRooms || [];
                    this.officeObjects = data.officeObjects || [];
                    
                    this.showNotification('数据导入成功！', 'success');
                    resolve(data);
                } catch (error) {
                    console.error('导入数据失败:', error);
                    this.showNotification('导入失败，文件格式错误', 'error');
                    reject(error);
                }
            };
            reader.onerror = () => {
                this.showNotification('读取文件失败', 'error');
                reject(new Error('文件读取失败'));
            };
            reader.readAsText(file);
        });
    }

    /**
     * 清空所有数据
     */
    clearAllData() {
        this.employees = [];
        this.meetingRooms = [];
        this.officeObjects = [];
        localStorage.removeItem(this.storageKey);
        this.showNotification('所有数据已清空', 'info');
    }

    /**
     * 获取统计信息
     */
    getStatistics() {
        const occupiedSeats = this.employees.filter(emp => emp.seatId).length;
        const totalSeats = this.officeObjects.filter(obj => obj.type === 'seat').length;
        
        return {
            totalEmployees: this.employees.length,
            totalMeetingRooms: this.meetingRooms.length,
            totalSeats: totalSeats,
            occupiedSeats: occupiedSeats,
            availableSeats: totalSeats - occupiedSeats,
            occupancyRate: totalSeats > 0 ? (occupiedSeats / totalSeats * 100).toFixed(1) : 0
        };
    }
}
