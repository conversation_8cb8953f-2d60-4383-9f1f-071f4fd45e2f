/**
 * 画布管理器 - 负责Fabric.js画布的管理和绘图功能
 */
class CanvasManager {
    constructor(canvasId, dataManager) {
        this.canvas = new fabric.Canvas(canvasId, {
            width: 1200,
            height: 800,
            backgroundColor: '#ffffff'
        });

        this.dataManager = dataManager;
        this.currentTool = null;
        this.isDrawing = false;
        this.zoomLevel = 1;
        this.selectedObject = null;
        this.selectedObjects = [];
        this.alignmentGuides = [];
        this.isAlignmentEnabled = true;
        this.alignmentThreshold = 10;
        this.contextMenu = null;
        this.isSelectionMode = false;
        this.selectionBox = null;
        this.selectionStart = null;

        this.initializeCanvas();
        this.setupEventListeners();
        this.setupContextMenu();
        this.setupAlignmentSystem();
    }

    /**
     * 初始化画布设置
     */
    initializeCanvas() {
        // 设置画布选择样式
        fabric.Object.prototype.set({
            transparentCorners: false,
            cornerColor: '#667eea',
            cornerStyle: 'circle',
            cornerSize: 10,
            borderColor: '#667eea',
            borderScaleFactor: 2,
            selectable: true,
            evented: true
        });

        // 启用对象缓存以提高性能
        fabric.Object.prototype.objectCaching = true;

        // 启用画布选择功能
        this.canvas.selection = true;
        this.canvas.preserveObjectStacking = true;

        // 设置画布背景网格
        this.drawGrid();
    }

    /**
     * 绘制网格背景
     */
    drawGrid() {
        const gridSize = 20;
        const width = this.canvas.width;
        const height = this.canvas.height;

        // 创建网格线
        const lines = [];
        
        // 垂直线
        for (let i = 0; i <= width; i += gridSize) {
            lines.push(new fabric.Line([i, 0, i, height], {
                stroke: '#f0f0f0',
                strokeWidth: 1,
                selectable: false,
                evented: false
            }));
        }
        
        // 水平线
        for (let i = 0; i <= height; i += gridSize) {
            lines.push(new fabric.Line([0, i, width, i], {
                stroke: '#f0f0f0',
                strokeWidth: 1,
                selectable: false,
                evented: false
            }));
        }

        // 添加网格到画布
        lines.forEach(line => {
            line.selectable = false;
            line.evented = false;
            this.canvas.add(line);
            this.canvas.sendToBack(line);
        });
    }

    /**
     * 设置事件监听器
     */
    setupEventListeners() {
        // 鼠标按下事件
        this.canvas.on('mouse:down', (e) => {
            if (this.currentTool && e.e.target === this.canvas.upperCanvasEl) {
                this.startDrawing(e);
            }
        });

        // 鼠标移动事件
        this.canvas.on('mouse:move', (e) => {
            if (this.isDrawing) {
                this.updateDrawing(e);
            }
        });

        // 鼠标释放事件
        this.canvas.on('mouse:up', (e) => {
            if (this.isDrawing) {
                this.finishDrawing(e);
            }
        });

        // 对象选中事件
        this.canvas.on('selection:created', (e) => {
            this.selectedObject = e.selected[0];
            this.updatePropertiesPanel();
        });

        this.canvas.on('selection:updated', (e) => {
            this.selectedObject = e.selected[0];
            this.updatePropertiesPanel();
        });

        this.canvas.on('selection:cleared', () => {
            this.selectedObject = null;
            this.clearPropertiesPanel();
        });

        // 对象修改事件
        this.canvas.on('object:modified', () => {
            this.saveCanvasState();
        });
    }

    /**
     * 设置当前绘图工具
     */
    setTool(toolType) {
        this.currentTool = toolType;
        this.canvas.isDrawingMode = false;

        // 设置选择模式 - 当没有工具选中时启用选择
        this.canvas.selection = toolType === null;

        // 设置对象的可选择性
        this.canvas.forEachObject((obj) => {
            if (obj.objectType !== 'grid-line' && obj.objectType !== 'alignment-guide') {
                obj.selectable = toolType === null;
                obj.evented = toolType === null;
            }
        });

        // 更新光标样式
        const canvasWrapper = document.querySelector('.canvas-wrapper');
        canvasWrapper.className = 'canvas-wrapper';
        if (toolType) {
            canvasWrapper.classList.add('drawing-mode');
        } else {
            canvasWrapper.classList.add('selection-mode');
        }

        this.canvas.renderAll();
    }

    /**
     * 开始绘制
     */
    startDrawing(e) {
        this.isDrawing = true;
        const pointer = this.canvas.getPointer(e.e);
        
        switch (this.currentTool) {
            case 'seat':
                this.createSeat(pointer);
                break;
            case 'meeting-room':
                this.createMeetingRoom(pointer);
                break;
            case 'reception':
                this.createReception(pointer);
                break;
            case 'wall':
                this.startWall(pointer);
                break;
            case 'door':
                this.createDoor(pointer);
                break;
            case 'text':
                this.createText(pointer);
                break;
        }
    }

    /**
     * 更新绘制过程
     */
    updateDrawing(e) {
        if (!this.isDrawing) return;
        
        const pointer = this.canvas.getPointer(e.e);
        
        if (this.currentTool === 'wall' && this.tempWall) {
            this.tempWall.set({
                x2: pointer.x,
                y2: pointer.y
            });
            this.canvas.renderAll();
        }
    }

    /**
     * 完成绘制
     */
    finishDrawing(e) {
        this.isDrawing = false;
        
        if (this.currentTool === 'wall' && this.tempWall) {
            this.tempWall.setCoords();
            this.tempWall = null;
        }
        
        this.saveCanvasState();
    }

    /**
     * 创建座位
     */
    createSeat(pointer) {
        const seat = new fabric.Rect({
            left: pointer.x - 25,
            top: pointer.y - 25,
            width: 50,
            height: 50,
            fill: '#e3f2fd',
            stroke: '#1976d2',
            strokeWidth: 2,
            rx: 5,
            ry: 5,
            objectType: 'seat',
            seatId: this.dataManager.generateId(),
            employeeId: null,
            label: '空座位'
        });

        // 添加座位标签
        const label = new fabric.Text('空座位', {
            left: pointer.x,
            top: pointer.y,
            fontSize: 12,
            fill: '#333',
            textAlign: 'center',
            originX: 'center',
            originY: 'center',
            selectable: false,
            evented: false
        });

        const group = new fabric.Group([seat, label], {
            left: pointer.x - 25,
            top: pointer.y - 25,
            objectType: 'seat',
            seatId: seat.seatId,
            employeeId: null,
            selectable: true,
            evented: true
        });

        this.canvas.add(group);

        // 如果当前没有选择工具，则选中新创建的对象
        if (!this.currentTool) {
            this.canvas.setActiveObject(group);
        }
    }

    /**
     * 创建会议室
     */
    createMeetingRoom(pointer) {
        const room = new fabric.Rect({
            left: pointer.x - 60,
            top: pointer.y - 40,
            width: 120,
            height: 80,
            fill: '#f3e5f5',
            stroke: '#7b1fa2',
            strokeWidth: 2,
            rx: 8,
            ry: 8,
            objectType: 'meeting-room',
            roomId: this.dataManager.generateId(),
            roomName: '会议室'
        });

        const label = new fabric.Text('会议室', {
            left: pointer.x,
            top: pointer.y,
            fontSize: 14,
            fill: '#333',
            textAlign: 'center',
            originX: 'center',
            originY: 'center',
            selectable: false,
            evented: false
        });

        const group = new fabric.Group([room, label], {
            left: pointer.x - 60,
            top: pointer.y - 40,
            objectType: 'meeting-room',
            roomId: room.roomId,
            selectable: true,
            evented: true
        });

        this.canvas.add(group);

        // 如果当前没有选择工具，则选中新创建的对象
        if (!this.currentTool) {
            this.canvas.setActiveObject(group);
        }
    }

    /**
     * 创建前台
     */
    createReception(pointer) {
        const reception = new fabric.Rect({
            left: pointer.x - 50,
            top: pointer.y - 30,
            width: 100,
            height: 60,
            fill: '#e8f5e8',
            stroke: '#388e3c',
            strokeWidth: 2,
            rx: 6,
            ry: 6,
            objectType: 'reception'
        });

        const label = new fabric.Text('前台', {
            left: pointer.x,
            top: pointer.y,
            fontSize: 14,
            fill: '#333',
            textAlign: 'center',
            originX: 'center',
            originY: 'center',
            selectable: false,
            evented: false
        });

        const group = new fabric.Group([reception, label], {
            left: pointer.x - 50,
            top: pointer.y - 30,
            objectType: 'reception',
            selectable: true,
            evented: true
        });

        this.canvas.add(group);

        // 如果当前没有选择工具，则选中新创建的对象
        if (!this.currentTool) {
            this.canvas.setActiveObject(group);
        }
    }

    /**
     * 开始绘制墙体
     */
    startWall(pointer) {
        this.tempWall = new fabric.Line([pointer.x, pointer.y, pointer.x, pointer.y], {
            stroke: '#424242',
            strokeWidth: 8,
            objectType: 'wall'
        });
        
        this.canvas.add(this.tempWall);
    }

    /**
     * 创建门
     */
    createDoor(pointer) {
        const door = new fabric.Rect({
            left: pointer.x - 15,
            top: pointer.y - 30,
            width: 30,
            height: 60,
            fill: '#fff3e0',
            stroke: '#f57c00',
            strokeWidth: 2,
            rx: 3,
            ry: 3,
            objectType: 'door'
        });

        door.selectable = true;
        door.evented = true;

        this.canvas.add(door);

        // 如果当前没有选择工具，则选中新创建的对象
        if (!this.currentTool) {
            this.canvas.setActiveObject(door);
        }
    }

    /**
     * 创建文本标记
     */
    createText(pointer) {
        const text = new fabric.IText('双击编辑文本', {
            left: pointer.x,
            top: pointer.y,
            fontSize: 16,
            fill: '#333',
            objectType: 'text'
        });

        text.selectable = true;
        text.evented = true;

        this.canvas.add(text);

        // 如果当前没有选择工具，则选中新创建的对象
        if (!this.currentTool) {
            this.canvas.setActiveObject(text);
        }
    }

    /**
     * 更新属性面板
     */
    updatePropertiesPanel() {
        const panel = document.getElementById('propertiesPanel');
        if (!this.selectedObject) {
            this.clearPropertiesPanel();
            return;
        }

        const objectType = this.selectedObject.objectType || this.selectedObject.get('objectType');
        let html = '';

        switch (objectType) {
            case 'seat':
                html = this.createSeatProperties();
                break;
            case 'meeting-room':
                html = this.createMeetingRoomProperties();
                break;
            case 'text':
                html = this.createTextProperties();
                break;
            default:
                html = this.createGeneralProperties();
        }

        panel.innerHTML = html;
        this.bindPropertyEvents();
    }

    /**
     * 创建座位属性面板
     */
    createSeatProperties() {
        const seatId = this.selectedObject.seatId || this.selectedObject.get('seatId');
        const employee = this.dataManager.getEmployeeBySeatId(seatId);
        
        return `
            <div class="property-group">
                <label>座位编号</label>
                <input type="text" id="seatId" value="${seatId || ''}" readonly>
            </div>
            <div class="property-group">
                <label>员工姓名</label>
                <input type="text" id="employeeName" value="${employee ? employee.name : ''}" placeholder="输入员工姓名">
            </div>
            <div class="property-group">
                <label>员工工号</label>
                <input type="text" id="employeeId" value="${employee ? employee.employeeId : ''}" placeholder="输入员工工号">
            </div>
            <div class="property-group">
                <label>部门</label>
                <input type="text" id="department" value="${employee ? employee.department : ''}" placeholder="输入部门">
            </div>
            <button class="btn btn-primary" onclick="canvasManager.updateSeatInfo()">更新座位信息</button>
        `;
    }

    /**
     * 创建会议室属性面板
     */
    createMeetingRoomProperties() {
        const roomId = this.selectedObject.roomId || this.selectedObject.get('roomId');
        const room = this.dataManager.getMeetingRoomByObjectId(roomId);
        
        return `
            <div class="property-group">
                <label>会议室名称</label>
                <input type="text" id="roomName" value="${room ? room.name : '会议室'}" placeholder="输入会议室名称">
            </div>
            <div class="property-group">
                <label>容纳人数</label>
                <input type="number" id="roomCapacity" value="${room ? room.capacity : 0}" min="0">
            </div>
            <div class="property-group">
                <label>设备信息</label>
                <textarea id="roomEquipment" placeholder="输入设备信息">${room ? room.equipment.join(', ') : ''}</textarea>
            </div>
            <button class="btn btn-primary" onclick="canvasManager.updateRoomInfo()">更新会议室信息</button>
        `;
    }

    /**
     * 创建文本属性面板
     */
    createTextProperties() {
        return `
            <div class="property-group">
                <label>文本内容</label>
                <textarea id="textContent">${this.selectedObject.text || ''}</textarea>
            </div>
            <div class="property-group">
                <label>字体大小</label>
                <input type="number" id="fontSize" value="${this.selectedObject.fontSize || 16}" min="8" max="72">
            </div>
            <div class="property-group">
                <label>文本颜色</label>
                <input type="color" id="textColor" value="${this.selectedObject.fill || '#333333'}">
            </div>
            <button class="btn btn-primary" onclick="canvasManager.updateTextProperties()">更新文本属性</button>
        `;
    }

    /**
     * 创建通用属性面板
     */
    createGeneralProperties() {
        return `
            <div class="property-group">
                <label>X坐标</label>
                <input type="number" id="objectX" value="${Math.round(this.selectedObject.left || 0)}">
            </div>
            <div class="property-group">
                <label>Y坐标</label>
                <input type="number" id="objectY" value="${Math.round(this.selectedObject.top || 0)}">
            </div>
            <div class="property-group">
                <label>旋转角度</label>
                <input type="number" id="objectAngle" value="${Math.round(this.selectedObject.angle || 0)}" min="0" max="360">
            </div>
            <button class="btn btn-primary" onclick="canvasManager.updateGeneralProperties()">更新属性</button>
            <button class="btn btn-danger" onclick="canvasManager.deleteSelectedObject()">删除对象</button>
        `;
    }

    /**
     * 清空属性面板
     */
    clearPropertiesPanel() {
        const panel = document.getElementById('propertiesPanel');
        panel.innerHTML = '<p class="no-selection">请选择一个对象来编辑属性</p>';
    }

    /**
     * 绑定属性面板事件
     */
    bindPropertyEvents() {
        // 这里可以添加实时更新的事件监听器
    }

    /**
     * 更新座位信息
     */
    updateSeatInfo() {
        const name = document.getElementById('employeeName').value;
        const employeeId = document.getElementById('employeeId').value;
        const department = document.getElementById('department').value;
        const seatId = this.selectedObject.seatId || this.selectedObject.get('seatId');

        if (name && employeeId) {
            // 添加或更新员工信息
            const employeeData = {
                name: name,
                employeeId: employeeId,
                department: department,
                seatId: seatId
            };

            this.dataManager.addEmployee(employeeData);
            
            // 更新座位显示
            this.updateSeatDisplay(this.selectedObject, name);
            this.dataManager.showNotification('座位信息更新成功！', 'success');
        } else {
            this.dataManager.showNotification('请填写员工姓名和工号', 'warning');
        }
    }

    /**
     * 更新座位显示
     */
    updateSeatDisplay(seatObject, employeeName) {
        // 更新座位颜色表示已占用
        if (seatObject.type === 'group') {
            const rect = seatObject.getObjects()[0];
            const text = seatObject.getObjects()[1];
            
            rect.set({
                fill: '#ffebee',
                stroke: '#d32f2f'
            });
            
            text.set({
                text: employeeName
            });
        }
        
        this.canvas.renderAll();
    }

    /**
     * 更新会议室信息
     */
    updateRoomInfo() {
        const name = document.getElementById('roomName').value;
        const capacity = parseInt(document.getElementById('roomCapacity').value) || 0;
        const equipment = document.getElementById('roomEquipment').value.split(',').map(item => item.trim()).filter(item => item);
        const roomId = this.selectedObject.roomId || this.selectedObject.get('roomId');

        const roomData = {
            name: name,
            capacity: capacity,
            equipment: equipment,
            objectId: roomId
        };

        this.dataManager.addMeetingRoom(roomData);
        
        // 更新会议室显示
        if (this.selectedObject.type === 'group') {
            const text = this.selectedObject.getObjects()[1];
            text.set({ text: name });
        }
        
        this.canvas.renderAll();
        this.dataManager.showNotification('会议室信息更新成功！', 'success');
    }

    /**
     * 更新文本属性
     */
    updateTextProperties() {
        const content = document.getElementById('textContent').value;
        const fontSize = parseInt(document.getElementById('fontSize').value) || 16;
        const color = document.getElementById('textColor').value;

        this.selectedObject.set({
            text: content,
            fontSize: fontSize,
            fill: color
        });

        this.canvas.renderAll();
        this.dataManager.showNotification('文本属性更新成功！', 'success');
    }

    /**
     * 更新通用属性
     */
    updateGeneralProperties() {
        const x = parseInt(document.getElementById('objectX').value) || 0;
        const y = parseInt(document.getElementById('objectY').value) || 0;
        const angle = parseInt(document.getElementById('objectAngle').value) || 0;

        this.selectedObject.set({
            left: x,
            top: y,
            angle: angle
        });

        this.selectedObject.setCoords();
        this.canvas.renderAll();
        this.dataManager.showNotification('属性更新成功！', 'success');
    }

    /**
     * 删除选中对象
     */
    deleteSelectedObject() {
        if (this.selectedObject) {
            this.canvas.remove(this.selectedObject);
            this.selectedObject = null;
            this.clearPropertiesPanel();
            this.dataManager.showNotification('对象已删除', 'info');
        }
    }

    /**
     * 缩放控制
     */
    zoomIn() {
        this.zoomLevel = Math.min(this.zoomLevel * 1.2, 3);
        this.canvas.setZoom(this.zoomLevel);
        this.updateZoomDisplay();
    }

    zoomOut() {
        this.zoomLevel = Math.max(this.zoomLevel / 1.2, 0.3);
        this.canvas.setZoom(this.zoomLevel);
        this.updateZoomDisplay();
    }

    resetZoom() {
        this.zoomLevel = 1;
        this.canvas.setZoom(1);
        this.canvas.viewportTransform = [1, 0, 0, 1, 0, 0];
        this.canvas.renderAll();
        this.updateZoomDisplay();
    }

    updateZoomDisplay() {
        document.getElementById('zoomLevel').textContent = Math.round(this.zoomLevel * 100) + '%';
    }

    /**
     * 保存画布状态
     */
    saveCanvasState() {
        // 这里可以实现撤销/重做功能的状态保存
    }

    /**
     * 清空画布
     */
    clearCanvas() {
        this.canvas.clear();
        this.drawGrid();
        this.selectedObject = null;
        this.clearPropertiesPanel();
    }

    /**
     * 获取画布数据
     */
    getCanvasData() {
        return this.canvas.toJSON(['objectType', 'seatId', 'employeeId', 'roomId', 'roomName']);
    }

    /**
     * 加载画布数据
     */
    loadCanvasData(data) {
        this.canvas.loadFromJSON(data, () => {
            this.canvas.renderAll();
            this.drawGrid();
        });
    }

    /**
     * 批量创建座位
     */
    showBatchSeatDialog() {
        const modalBody = document.getElementById('modalBody');
        modalBody.innerHTML = `
            <h3>批量添加座位</h3>
            <div class="batch-seat-form">
                <div class="batch-input-row">
                    <label>布局:</label>
                    <input type="text" id="batchLayout" placeholder="例如: 5*10 或 5x10" value="5*10">
                    <small>格式: 横向数量*纵向数量</small>
                </div>
                <div class="batch-input-row">
                    <label>座位间距:</label>
                    <input type="number" id="seatSpacing" value="80" min="60" max="200">
                    <small>像素</small>
                </div>
                <div class="batch-input-row">
                    <label>座位大小:</label>
                    <input type="number" id="seatSize" value="50" min="30" max="100">
                    <small>像素</small>
                </div>
                <div class="batch-input-row">
                    <label>起始编号:</label>
                    <input type="text" id="startNumber" value="A001" placeholder="例如: A001">
                </div>
                <div class="batch-preview" id="batchPreview">
                    <h4>预览</h4>
                    <div class="batch-preview-info">请输入布局参数</div>
                </div>
            </div>
            <div class="modal-actions">
                <button class="btn btn-primary" onclick="canvasManager.createBatchSeats()">创建座位</button>
                <button class="btn btn-secondary" onclick="canvasManager.updateBatchPreview()">预览</button>
                <button class="btn btn-secondary" onclick="searchManager.closeModal()">取消</button>
            </div>
        `;

        // 绑定实时预览事件
        const inputs = modalBody.querySelectorAll('input');
        inputs.forEach(input => {
            input.addEventListener('input', () => {
                this.updateBatchPreview();
            });
        });

        searchManager.showModal();
        this.updateBatchPreview();
    }

    /**
     * 更新批量座位预览
     */
    updateBatchPreview() {
        const layout = document.getElementById('batchLayout').value;
        const spacing = parseInt(document.getElementById('seatSpacing').value) || 80;
        const size = parseInt(document.getElementById('seatSize').value) || 50;
        const startNumber = document.getElementById('startNumber').value || 'A001';

        const previewDiv = document.getElementById('batchPreview');

        // 解析布局
        const match = layout.match(/(\d+)[*x×](\d+)/i);
        if (!match) {
            previewDiv.innerHTML = `
                <h4>预览</h4>
                <div class="batch-preview-info" style="color: #f44336;">
                    格式错误，请使用 "横向*纵向" 格式，例如: 5*10
                </div>
            `;
            return;
        }

        const cols = parseInt(match[1]);
        const rows = parseInt(match[2]);
        const totalSeats = cols * rows;

        if (totalSeats > 100) {
            previewDiv.innerHTML = `
                <h4>预览</h4>
                <div class="batch-preview-info" style="color: #f44336;">
                    座位数量过多 (${totalSeats})，建议不超过100个
                </div>
            `;
            return;
        }

        // 创建预览网格
        const gridHtml = `
            <h4>预览</h4>
            <div class="batch-preview-info">
                <strong>布局:</strong> ${cols} × ${rows} = ${totalSeats} 个座位<br>
                <strong>尺寸:</strong> ${cols * spacing}px × ${rows * spacing}px<br>
                <strong>编号:</strong> ${startNumber} ~ ${this.generateSeatNumber(startNumber, totalSeats - 1)}
            </div>
            <div class="batch-preview-grid" style="grid-template-columns: repeat(${Math.min(cols, 10)}, 1fr);">
                ${Array.from({length: Math.min(totalSeats, 50)}, (_, i) =>
                    `<div class="batch-preview-seat" title="${this.generateSeatNumber(startNumber, i)}"></div>`
                ).join('')}
                ${totalSeats > 50 ? '<div style="grid-column: 1/-1; text-align: center; color: #666; font-size: 0.8rem;">... 还有更多座位</div>' : ''}
            </div>
        `;

        previewDiv.innerHTML = gridHtml;
    }

    /**
     * 生成座位编号
     */
    generateSeatNumber(startNumber, index) {
        // 提取字母前缀和数字部分
        const match = startNumber.match(/^([A-Za-z]*)(\d+)$/);
        if (match) {
            const prefix = match[1];
            const startNum = parseInt(match[2]);
            return prefix + String(startNum + index).padStart(match[2].length, '0');
        }
        return startNumber + index;
    }

    /**
     * 创建批量座位
     */
    createBatchSeats() {
        const layout = document.getElementById('batchLayout').value;
        const spacing = parseInt(document.getElementById('seatSpacing').value) || 80;
        const size = parseInt(document.getElementById('seatSize').value) || 50;
        const startNumber = document.getElementById('startNumber').value || 'A001';

        // 解析布局
        const match = layout.match(/(\d+)[*x×](\d+)/i);
        if (!match) {
            this.dataManager.showNotification('布局格式错误', 'error');
            return;
        }

        const cols = parseInt(match[1]);
        const rows = parseInt(match[2]);
        const totalSeats = cols * rows;

        if (totalSeats > 100) {
            this.dataManager.showNotification('座位数量过多，建议不超过100个', 'warning');
            return;
        }

        // 获取画布中心点作为起始位置
        const centerX = this.canvas.width / 2;
        const centerY = this.canvas.height / 2;
        const startX = centerX - (cols * spacing) / 2;
        const startY = centerY - (rows * spacing) / 2;

        // 创建座位组
        const seats = [];
        for (let row = 0; row < rows; row++) {
            for (let col = 0; col < cols; col++) {
                const x = startX + col * spacing;
                const y = startY + row * spacing;
                const seatNumber = this.generateSeatNumber(startNumber, row * cols + col);

                const seat = this.createSingleSeat(x, y, size, seatNumber);
                seats.push(seat);
            }
        }

        // 添加到画布
        seats.forEach(seat => {
            this.canvas.add(seat);
        });

        this.canvas.renderAll();
        searchManager.closeModal();

        this.dataManager.showNotification(`成功创建 ${totalSeats} 个座位`, 'success');
    }

    /**
     * 创建单个座位
     */
    createSingleSeat(x, y, size, seatNumber) {
        const seat = new fabric.Rect({
            left: x - size/2,
            top: y - size/2,
            width: size,
            height: size,
            fill: '#e3f2fd',
            stroke: '#1976d2',
            strokeWidth: 2,
            rx: 5,
            ry: 5,
            objectType: 'seat',
            seatId: this.dataManager.generateId(),
            employeeId: null,
            seatNumber: seatNumber
        });

        const label = new fabric.Text(seatNumber, {
            left: x,
            top: y,
            fontSize: Math.max(8, size * 0.2),
            fill: '#333',
            textAlign: 'center',
            originX: 'center',
            originY: 'center',
            selectable: false,
            evented: false
        });

        const group = new fabric.Group([seat, label], {
            left: x - size/2,
            top: y - size/2,
            objectType: 'seat',
            seatId: seat.seatId,
            seatNumber: seatNumber,
            employeeId: null,
            selectable: true,
            evented: true
        });

        return group;
    }

    /**
     * 设置对齐系统
     */
    setupAlignmentSystem() {
        // 创建对齐辅助线容器
        this.alignmentGuides = {
            horizontal: [],
            vertical: []
        };

        // 监听对象移动事件
        this.canvas.on('object:moving', (e) => {
            if (this.isAlignmentEnabled) {
                this.showAlignmentGuides(e.target);
            }
        });

        this.canvas.on('object:moved', () => {
            this.hideAlignmentGuides();
        });
    }

    /**
     * 显示对齐辅助线
     */
    showAlignmentGuides(movingObject) {
        this.hideAlignmentGuides();

        const objects = this.canvas.getObjects().filter(obj =>
            obj !== movingObject && obj.objectType !== 'grid-line'
        );

        const movingBounds = movingObject.getBoundingRect();
        const movingCenterX = movingBounds.left + movingBounds.width / 2;
        const movingCenterY = movingBounds.top + movingBounds.height / 2;

        const threshold = this.alignmentThreshold;
        let snapX = null;
        let snapY = null;

        // 检查水平对齐
        objects.forEach(obj => {
            const bounds = obj.getBoundingRect();
            const centerX = bounds.left + bounds.width / 2;
            const centerY = bounds.top + bounds.height / 2;

            // 垂直中心对齐
            if (Math.abs(movingCenterX - centerX) < threshold) {
                snapX = centerX;
                this.createAlignmentGuide('vertical', centerX, 0, this.canvas.height);
            }

            // 水平中心对齐
            if (Math.abs(movingCenterY - centerY) < threshold) {
                snapY = centerY;
                this.createAlignmentGuide('horizontal', 0, centerY, this.canvas.width);
            }

            // 边缘对齐
            if (Math.abs(movingBounds.left - bounds.left) < threshold) {
                snapX = bounds.left + movingBounds.width / 2;
                this.createAlignmentGuide('vertical', bounds.left, 0, this.canvas.height);
            }

            if (Math.abs(movingBounds.top - bounds.top) < threshold) {
                snapY = bounds.top + movingBounds.height / 2;
                this.createAlignmentGuide('horizontal', 0, bounds.top, this.canvas.width);
            }
        });

        // 应用吸附
        if (snapX !== null) {
            movingObject.set('left', snapX - movingBounds.width / 2);
        }
        if (snapY !== null) {
            movingObject.set('top', snapY - movingBounds.height / 2);
        }

        if (snapX !== null || snapY !== null) {
            movingObject.setCoords();
            this.canvas.renderAll();
        }
    }

    /**
     * 创建对齐辅助线
     */
    createAlignmentGuide(type, x, y, length) {
        const guide = new fabric.Line(
            type === 'vertical' ? [x, y, x, y + length] : [x, y, x + length, y],
            {
                stroke: '#ff4444',
                strokeWidth: 1,
                strokeDashArray: [5, 5],
                selectable: false,
                evented: false,
                objectType: 'alignment-guide'
            }
        );

        this.canvas.add(guide);
        this.alignmentGuides[type].push(guide);
    }

    /**
     * 隐藏对齐辅助线
     */
    hideAlignmentGuides() {
        [...this.alignmentGuides.horizontal, ...this.alignmentGuides.vertical].forEach(guide => {
            this.canvas.remove(guide);
        });
        this.alignmentGuides.horizontal = [];
        this.alignmentGuides.vertical = [];
    }

    /**
     * 设置右键菜单
     */
    setupContextMenu() {
        // 创建右键菜单元素
        this.contextMenu = document.createElement('div');
        this.contextMenu.className = 'context-menu';
        this.contextMenu.style.display = 'none';
        document.body.appendChild(this.contextMenu);

        // 监听右键事件
        this.canvas.on('mouse:down', (e) => {
            if (e.e.button === 2) { // 右键
                e.e.preventDefault();
                this.showContextMenu(e);
            } else {
                this.hideContextMenu();
            }
        });

        // 点击其他地方隐藏菜单
        document.addEventListener('click', () => {
            this.hideContextMenu();
        });

        // 阻止画布右键默认菜单
        this.canvas.upperCanvasEl.addEventListener('contextmenu', (e) => {
            e.preventDefault();
        });
    }

    /**
     * 显示右键菜单
     */
    showContextMenu(e) {
        const pointer = this.canvas.getPointer(e.e);
        const target = this.canvas.findTarget(e.e);

        let menuItems = [];

        if (target && target.objectType) {
            // 有选中对象的菜单
            menuItems = [
                { icon: '✏️', text: '编辑属性', action: () => this.editObjectProperties(target) },
                { icon: '📋', text: '复制', action: () => this.copyObject(target) },
                { icon: '📐', text: '对齐', action: () => this.showAlignOptions(target) },
                { type: 'separator' },
                { icon: '🗑️', text: '删除', action: () => this.deleteObject(target), class: 'danger' }
            ];

            if (target.objectType === 'seat') {
                menuItems.unshift({ icon: '👤', text: '分配员工', action: () => this.assignEmployee(target) });
            }
        } else {
            // 空白区域的菜单
            menuItems = [
                { icon: '📋', text: '粘贴', action: () => this.pasteObject(pointer) },
                { icon: '💺', text: '添加座位', action: () => this.createSeatAt(pointer) },
                { icon: '🏢', text: '添加会议室', action: () => this.createMeetingRoomAt(pointer) },
                { type: 'separator' },
                { icon: '📋', text: '批量座位', action: () => this.showBatchSeatDialog() },
                { icon: '🔍', text: '适应画布', action: () => this.fitToCanvas() }
            ];
        }

        this.renderContextMenu(menuItems, e.e.clientX, e.e.clientY);
    }

    /**
     * 渲染右键菜单
     */
    renderContextMenu(items, x, y) {
        const html = items.map(item => {
            if (item.type === 'separator') {
                return '<div class="context-menu-separator"></div>';
            }

            const className = `context-menu-item ${item.class || ''}`;
            return `<div class="${className}" data-action="${items.indexOf(item)}">
                ${item.icon} ${item.text}
            </div>`;
        }).join('');

        this.contextMenu.innerHTML = html;
        this.contextMenu.style.display = 'block';
        this.contextMenu.style.left = x + 'px';
        this.contextMenu.style.top = y + 'px';

        // 确保菜单不超出屏幕
        const rect = this.contextMenu.getBoundingRect();
        if (rect.right > window.innerWidth) {
            this.contextMenu.style.left = (x - rect.width) + 'px';
        }
        if (rect.bottom > window.innerHeight) {
            this.contextMenu.style.top = (y - rect.height) + 'px';
        }

        // 绑定点击事件
        this.contextMenu.querySelectorAll('.context-menu-item').forEach((item, index) => {
            if (!item.classList.contains('disabled')) {
                item.addEventListener('click', () => {
                    const menuItem = items.filter(i => i.type !== 'separator')[
                        Array.from(this.contextMenu.querySelectorAll('.context-menu-item')).indexOf(item)
                    ];
                    if (menuItem && menuItem.action) {
                        menuItem.action();
                    }
                    this.hideContextMenu();
                });
            }
        });
    }

    /**
     * 隐藏右键菜单
     */
    hideContextMenu() {
        if (this.contextMenu) {
            this.contextMenu.style.display = 'none';
        }
    }

    /**
     * 高亮显示对象
     */
    highlightObject(objectId, objectType) {
        const objects = this.canvas.getObjects();
        let targetObject = null;

        // 查找目标对象
        objects.forEach(obj => {
            if (objectType === 'seat' && obj.seatId === objectId) {
                targetObject = obj;
            } else if (objectType === 'meeting-room' && obj.roomId === objectId) {
                targetObject = obj;
            }
        });

        if (targetObject) {
            // 清除之前的选择
            this.canvas.discardActiveObject();

            // 选中并居中显示目标对象
            this.canvas.setActiveObject(targetObject);
            this.canvas.centerObject(targetObject);
            this.canvas.renderAll();

            // 添加高亮效果
            targetObject.set({
                shadow: new fabric.Shadow({
                    color: '#ffc107',
                    blur: 20,
                    offsetX: 0,
                    offsetY: 0
                })
            });

            this.canvas.renderAll();

            // 3秒后移除高亮效果
            setTimeout(() => {
                targetObject.set({ shadow: null });
                this.canvas.renderAll();
            }, 3000);
        }
    }

    /**
     * 右键菜单功能实现
     */
    editObjectProperties(target) {
        this.canvas.setActiveObject(target);
        this.updatePropertiesPanel();
    }

    copyObject(target) {
        this.copiedObject = fabric.util.object.clone(target);
        this.dataManager.showNotification('对象已复制', 'success');
    }

    pasteObject(pointer) {
        if (this.copiedObject) {
            fabric.util.object.clone(this.copiedObject, (cloned) => {
                cloned.set({
                    left: pointer.x - 25,
                    top: pointer.y - 25,
                    seatId: this.dataManager.generateId()
                });
                this.canvas.add(cloned);
                this.canvas.setActiveObject(cloned);
                this.canvas.renderAll();
            });
            this.dataManager.showNotification('对象已粘贴', 'success');
        } else {
            this.dataManager.showNotification('没有可粘贴的对象', 'warning');
        }
    }

    deleteObject(target) {
        this.canvas.remove(target);
        this.canvas.renderAll();
        this.dataManager.showNotification('对象已删除', 'success');
    }

    createSeatAt(pointer) {
        this.createSeat(pointer);
    }

    createMeetingRoomAt(pointer) {
        this.createMeetingRoom(pointer);
    }

    assignEmployee(seatObject) {
        // 打开员工分配对话框
        this.canvas.setActiveObject(seatObject);
        this.updatePropertiesPanel();

        // 聚焦到员工姓名输入框
        setTimeout(() => {
            const nameInput = document.getElementById('employeeName');
            if (nameInput) {
                nameInput.focus();
            }
        }, 100);
    }

    showAlignOptions(target) {
        // 显示对齐工具栏
        this.showAlignToolbar();
        this.canvas.setActiveObject(target);
    }

    fitToCanvas() {
        const objects = this.canvas.getObjects().filter(obj =>
            obj.objectType !== 'grid-line' && obj.objectType !== 'alignment-guide'
        );

        if (objects.length === 0) return;

        const group = new fabric.Group(objects, { canvas: this.canvas });
        const bounds = group.getBoundingRect();

        // 计算缩放比例
        const scaleX = (this.canvas.width * 0.8) / bounds.width;
        const scaleY = (this.canvas.height * 0.8) / bounds.height;
        const scale = Math.min(scaleX, scaleY, 2); // 最大放大2倍

        this.zoomLevel = scale;
        this.canvas.setZoom(scale);

        // 居中显示
        const centerX = this.canvas.width / 2;
        const centerY = this.canvas.height / 2;
        const groupCenterX = bounds.left + bounds.width / 2;
        const groupCenterY = bounds.top + bounds.height / 2;

        this.canvas.relativePan({
            x: (centerX - groupCenterX * scale),
            y: (centerY - groupCenterY * scale)
        });

        this.updateZoomDisplay();
        this.dataManager.showNotification('已适应画布', 'success');

        // 清理临时组
        group.destroy();
    }

    /**
     * 显示对齐工具栏
     */
    showAlignToolbar() {
        let toolbar = document.getElementById('alignToolbar');
        if (!toolbar) {
            toolbar = document.createElement('div');
            toolbar.id = 'alignToolbar';
            toolbar.className = 'align-toolbar';
            toolbar.innerHTML = `
                <button class="align-btn" title="左对齐" onclick="canvasManager.alignObjects('left')">⬅️</button>
                <button class="align-btn" title="水平居中" onclick="canvasManager.alignObjects('centerH')">↔️</button>
                <button class="align-btn" title="右对齐" onclick="canvasManager.alignObjects('right')">➡️</button>
                <button class="align-btn" title="顶部对齐" onclick="canvasManager.alignObjects('top')">⬆️</button>
                <button class="align-btn" title="垂直居中" onclick="canvasManager.alignObjects('centerV')">↕️</button>
                <button class="align-btn" title="底部对齐" onclick="canvasManager.alignObjects('bottom')">⬇️</button>
                <button class="align-btn" title="水平分布" onclick="canvasManager.distributeObjects('horizontal')">⬌</button>
                <button class="align-btn" title="垂直分布" onclick="canvasManager.distributeObjects('vertical')">⬍</button>
                <button class="align-btn" title="关闭" onclick="canvasManager.hideAlignToolbar()">✖️</button>
            `;
            document.body.appendChild(toolbar);
        }

        toolbar.classList.add('show');

        // 5秒后自动隐藏
        clearTimeout(this.alignToolbarTimeout);
        this.alignToolbarTimeout = setTimeout(() => {
            this.hideAlignToolbar();
        }, 10000);
    }

    /**
     * 隐藏对齐工具栏
     */
    hideAlignToolbar() {
        const toolbar = document.getElementById('alignToolbar');
        if (toolbar) {
            toolbar.classList.remove('show');
        }
        clearTimeout(this.alignToolbarTimeout);
    }

    /**
     * 对齐对象
     */
    alignObjects(type) {
        const activeObjects = this.canvas.getActiveObjects();
        if (activeObjects.length < 2) {
            this.dataManager.showNotification('请选择至少2个对象进行对齐', 'warning');
            return;
        }

        const bounds = activeObjects.map(obj => obj.getBoundingRect());
        let reference;

        switch (type) {
            case 'left':
                reference = Math.min(...bounds.map(b => b.left));
                activeObjects.forEach((obj, i) => {
                    obj.set('left', reference);
                });
                break;
            case 'right':
                reference = Math.max(...bounds.map(b => b.left + b.width));
                activeObjects.forEach((obj, i) => {
                    obj.set('left', reference - bounds[i].width);
                });
                break;
            case 'top':
                reference = Math.min(...bounds.map(b => b.top));
                activeObjects.forEach((obj, i) => {
                    obj.set('top', reference);
                });
                break;
            case 'bottom':
                reference = Math.max(...bounds.map(b => b.top + b.height));
                activeObjects.forEach((obj, i) => {
                    obj.set('top', reference - bounds[i].height);
                });
                break;
            case 'centerH':
                reference = bounds.reduce((sum, b) => sum + b.left + b.width/2, 0) / bounds.length;
                activeObjects.forEach((obj, i) => {
                    obj.set('left', reference - bounds[i].width/2);
                });
                break;
            case 'centerV':
                reference = bounds.reduce((sum, b) => sum + b.top + b.height/2, 0) / bounds.length;
                activeObjects.forEach((obj, i) => {
                    obj.set('top', reference - bounds[i].height/2);
                });
                break;
        }

        activeObjects.forEach(obj => obj.setCoords());
        this.canvas.renderAll();
        this.dataManager.showNotification(`对象已${this.getAlignTypeName(type)}`, 'success');
    }

    /**
     * 分布对象
     */
    distributeObjects(direction) {
        const activeObjects = this.canvas.getActiveObjects();
        if (activeObjects.length < 3) {
            this.dataManager.showNotification('请选择至少3个对象进行分布', 'warning');
            return;
        }

        const bounds = activeObjects.map(obj => obj.getBoundingRect());

        if (direction === 'horizontal') {
            // 按X坐标排序
            const sorted = activeObjects.map((obj, i) => ({ obj, bounds: bounds[i] }))
                .sort((a, b) => a.bounds.left - b.bounds.left);

            const first = sorted[0].bounds;
            const last = sorted[sorted.length - 1].bounds;
            const totalSpace = (last.left + last.width) - first.left;
            const objectsWidth = sorted.reduce((sum, item) => sum + item.bounds.width, 0);
            const spacing = (totalSpace - objectsWidth) / (sorted.length - 1);

            let currentX = first.left;
            sorted.forEach((item, i) => {
                if (i > 0) {
                    currentX += spacing;
                    item.obj.set('left', currentX);
                }
                currentX += item.bounds.width;
            });
        } else {
            // 按Y坐标排序
            const sorted = activeObjects.map((obj, i) => ({ obj, bounds: bounds[i] }))
                .sort((a, b) => a.bounds.top - b.bounds.top);

            const first = sorted[0].bounds;
            const last = sorted[sorted.length - 1].bounds;
            const totalSpace = (last.top + last.height) - first.top;
            const objectsHeight = sorted.reduce((sum, item) => sum + item.bounds.height, 0);
            const spacing = (totalSpace - objectsHeight) / (sorted.length - 1);

            let currentY = first.top;
            sorted.forEach((item, i) => {
                if (i > 0) {
                    currentY += spacing;
                    item.obj.set('top', currentY);
                }
                currentY += item.bounds.height;
            });
        }

        activeObjects.forEach(obj => obj.setCoords());
        this.canvas.renderAll();
        this.dataManager.showNotification(`对象已${direction === 'horizontal' ? '水平' : '垂直'}分布`, 'success');
    }

    /**
     * 获取对齐类型名称
     */
    getAlignTypeName(type) {
        const names = {
            'left': '左对齐',
            'right': '右对齐',
            'top': '顶部对齐',
            'bottom': '底部对齐',
            'centerH': '水平居中对齐',
            'centerV': '垂直居中对齐'
        };
        return names[type] || '对齐';
    }

    /**
     * 切换选择模式
     */
    toggleSelectionMode() {
        this.isSelectionMode = !this.isSelectionMode;
        this.canvas.selection = this.isSelectionMode;

        if (this.isSelectionMode) {
            this.setTool(null);
            this.dataManager.showNotification('已启用选择模式', 'info');
        } else {
            this.dataManager.showNotification('已退出选择模式', 'info');
        }

        // 更新按钮状态
        const selectBtn = document.getElementById('selectModeBtn');
        if (selectBtn) {
            selectBtn.classList.toggle('active', this.isSelectionMode);
        }
    }
}
