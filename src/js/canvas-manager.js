/**
 * 画布管理器 - 负责Fabric.js画布的管理和绘图功能
 */
class CanvasManager {
    constructor(canvasId, dataManager) {
        this.canvas = new fabric.Canvas(canvasId, {
            width: 1200,
            height: 800,
            backgroundColor: '#ffffff'
        });
        
        this.dataManager = dataManager;
        this.currentTool = null;
        this.isDrawing = false;
        this.zoomLevel = 1;
        this.selectedObject = null;
        
        this.initializeCanvas();
        this.setupEventListeners();
    }

    /**
     * 初始化画布设置
     */
    initializeCanvas() {
        // 设置画布选择样式
        fabric.Object.prototype.set({
            transparentCorners: false,
            cornerColor: '#667eea',
            cornerStyle: 'circle',
            cornerSize: 10,
            borderColor: '#667eea',
            borderScaleFactor: 2
        });

        // 启用对象缓存以提高性能
        fabric.Object.prototype.objectCaching = true;
        
        // 设置画布背景网格
        this.drawGrid();
    }

    /**
     * 绘制网格背景
     */
    drawGrid() {
        const gridSize = 20;
        const width = this.canvas.width;
        const height = this.canvas.height;

        // 创建网格线
        const lines = [];
        
        // 垂直线
        for (let i = 0; i <= width; i += gridSize) {
            lines.push(new fabric.Line([i, 0, i, height], {
                stroke: '#f0f0f0',
                strokeWidth: 1,
                selectable: false,
                evented: false
            }));
        }
        
        // 水平线
        for (let i = 0; i <= height; i += gridSize) {
            lines.push(new fabric.Line([0, i, width, i], {
                stroke: '#f0f0f0',
                strokeWidth: 1,
                selectable: false,
                evented: false
            }));
        }

        // 添加网格到画布
        lines.forEach(line => {
            this.canvas.add(line);
            this.canvas.sendToBack(line);
        });
    }

    /**
     * 设置事件监听器
     */
    setupEventListeners() {
        // 鼠标按下事件
        this.canvas.on('mouse:down', (e) => {
            if (this.currentTool && e.e.target === this.canvas.upperCanvasEl) {
                this.startDrawing(e);
            }
        });

        // 鼠标移动事件
        this.canvas.on('mouse:move', (e) => {
            if (this.isDrawing) {
                this.updateDrawing(e);
            }
        });

        // 鼠标释放事件
        this.canvas.on('mouse:up', (e) => {
            if (this.isDrawing) {
                this.finishDrawing(e);
            }
        });

        // 对象选中事件
        this.canvas.on('selection:created', (e) => {
            this.selectedObject = e.selected[0];
            this.updatePropertiesPanel();
        });

        this.canvas.on('selection:updated', (e) => {
            this.selectedObject = e.selected[0];
            this.updatePropertiesPanel();
        });

        this.canvas.on('selection:cleared', () => {
            this.selectedObject = null;
            this.clearPropertiesPanel();
        });

        // 对象修改事件
        this.canvas.on('object:modified', () => {
            this.saveCanvasState();
        });
    }

    /**
     * 设置当前绘图工具
     */
    setTool(toolType) {
        this.currentTool = toolType;
        this.canvas.isDrawingMode = false;
        this.canvas.selection = toolType === null;
        
        // 更新光标样式
        const canvasWrapper = document.querySelector('.canvas-wrapper');
        canvasWrapper.className = 'canvas-wrapper';
        if (toolType) {
            canvasWrapper.classList.add('drawing-mode');
        } else {
            canvasWrapper.classList.add('selection-mode');
        }
    }

    /**
     * 开始绘制
     */
    startDrawing(e) {
        this.isDrawing = true;
        const pointer = this.canvas.getPointer(e.e);
        
        switch (this.currentTool) {
            case 'seat':
                this.createSeat(pointer);
                break;
            case 'meeting-room':
                this.createMeetingRoom(pointer);
                break;
            case 'reception':
                this.createReception(pointer);
                break;
            case 'wall':
                this.startWall(pointer);
                break;
            case 'door':
                this.createDoor(pointer);
                break;
            case 'text':
                this.createText(pointer);
                break;
        }
    }

    /**
     * 更新绘制过程
     */
    updateDrawing(e) {
        if (!this.isDrawing) return;
        
        const pointer = this.canvas.getPointer(e.e);
        
        if (this.currentTool === 'wall' && this.tempWall) {
            this.tempWall.set({
                x2: pointer.x,
                y2: pointer.y
            });
            this.canvas.renderAll();
        }
    }

    /**
     * 完成绘制
     */
    finishDrawing(e) {
        this.isDrawing = false;
        
        if (this.currentTool === 'wall' && this.tempWall) {
            this.tempWall.setCoords();
            this.tempWall = null;
        }
        
        this.saveCanvasState();
    }

    /**
     * 创建座位
     */
    createSeat(pointer) {
        const seat = new fabric.Rect({
            left: pointer.x - 25,
            top: pointer.y - 25,
            width: 50,
            height: 50,
            fill: '#e3f2fd',
            stroke: '#1976d2',
            strokeWidth: 2,
            rx: 5,
            ry: 5,
            objectType: 'seat',
            seatId: this.dataManager.generateId(),
            employeeId: null,
            label: '空座位'
        });

        // 添加座位标签
        const label = new fabric.Text('空座位', {
            left: pointer.x,
            top: pointer.y,
            fontSize: 12,
            fill: '#333',
            textAlign: 'center',
            originX: 'center',
            originY: 'center',
            selectable: false,
            evented: false
        });

        const group = new fabric.Group([seat, label], {
            left: pointer.x - 25,
            top: pointer.y - 25,
            objectType: 'seat',
            seatId: seat.seatId,
            employeeId: null
        });

        this.canvas.add(group);
        this.canvas.setActiveObject(group);
    }

    /**
     * 创建会议室
     */
    createMeetingRoom(pointer) {
        const room = new fabric.Rect({
            left: pointer.x - 60,
            top: pointer.y - 40,
            width: 120,
            height: 80,
            fill: '#f3e5f5',
            stroke: '#7b1fa2',
            strokeWidth: 2,
            rx: 8,
            ry: 8,
            objectType: 'meeting-room',
            roomId: this.dataManager.generateId(),
            roomName: '会议室'
        });

        const label = new fabric.Text('会议室', {
            left: pointer.x,
            top: pointer.y,
            fontSize: 14,
            fill: '#333',
            textAlign: 'center',
            originX: 'center',
            originY: 'center',
            selectable: false,
            evented: false
        });

        const group = new fabric.Group([room, label], {
            left: pointer.x - 60,
            top: pointer.y - 40,
            objectType: 'meeting-room',
            roomId: room.roomId
        });

        this.canvas.add(group);
        this.canvas.setActiveObject(group);
    }

    /**
     * 创建前台
     */
    createReception(pointer) {
        const reception = new fabric.Rect({
            left: pointer.x - 50,
            top: pointer.y - 30,
            width: 100,
            height: 60,
            fill: '#e8f5e8',
            stroke: '#388e3c',
            strokeWidth: 2,
            rx: 6,
            ry: 6,
            objectType: 'reception'
        });

        const label = new fabric.Text('前台', {
            left: pointer.x,
            top: pointer.y,
            fontSize: 14,
            fill: '#333',
            textAlign: 'center',
            originX: 'center',
            originY: 'center',
            selectable: false,
            evented: false
        });

        const group = new fabric.Group([reception, label], {
            left: pointer.x - 50,
            top: pointer.y - 30,
            objectType: 'reception'
        });

        this.canvas.add(group);
        this.canvas.setActiveObject(group);
    }

    /**
     * 开始绘制墙体
     */
    startWall(pointer) {
        this.tempWall = new fabric.Line([pointer.x, pointer.y, pointer.x, pointer.y], {
            stroke: '#424242',
            strokeWidth: 8,
            objectType: 'wall'
        });
        
        this.canvas.add(this.tempWall);
    }

    /**
     * 创建门
     */
    createDoor(pointer) {
        const door = new fabric.Rect({
            left: pointer.x - 15,
            top: pointer.y - 30,
            width: 30,
            height: 60,
            fill: '#fff3e0',
            stroke: '#f57c00',
            strokeWidth: 2,
            rx: 3,
            ry: 3,
            objectType: 'door'
        });

        this.canvas.add(door);
        this.canvas.setActiveObject(door);
    }

    /**
     * 创建文本标记
     */
    createText(pointer) {
        const text = new fabric.IText('双击编辑文本', {
            left: pointer.x,
            top: pointer.y,
            fontSize: 16,
            fill: '#333',
            objectType: 'text'
        });

        this.canvas.add(text);
        this.canvas.setActiveObject(text);
    }

    /**
     * 更新属性面板
     */
    updatePropertiesPanel() {
        const panel = document.getElementById('propertiesPanel');
        if (!this.selectedObject) {
            this.clearPropertiesPanel();
            return;
        }

        const objectType = this.selectedObject.objectType || this.selectedObject.get('objectType');
        let html = '';

        switch (objectType) {
            case 'seat':
                html = this.createSeatProperties();
                break;
            case 'meeting-room':
                html = this.createMeetingRoomProperties();
                break;
            case 'text':
                html = this.createTextProperties();
                break;
            default:
                html = this.createGeneralProperties();
        }

        panel.innerHTML = html;
        this.bindPropertyEvents();
    }

    /**
     * 创建座位属性面板
     */
    createSeatProperties() {
        const seatId = this.selectedObject.seatId || this.selectedObject.get('seatId');
        const employee = this.dataManager.getEmployeeBySeatId(seatId);
        
        return `
            <div class="property-group">
                <label>座位编号</label>
                <input type="text" id="seatId" value="${seatId || ''}" readonly>
            </div>
            <div class="property-group">
                <label>员工姓名</label>
                <input type="text" id="employeeName" value="${employee ? employee.name : ''}" placeholder="输入员工姓名">
            </div>
            <div class="property-group">
                <label>员工工号</label>
                <input type="text" id="employeeId" value="${employee ? employee.employeeId : ''}" placeholder="输入员工工号">
            </div>
            <div class="property-group">
                <label>部门</label>
                <input type="text" id="department" value="${employee ? employee.department : ''}" placeholder="输入部门">
            </div>
            <button class="btn btn-primary" onclick="canvasManager.updateSeatInfo()">更新座位信息</button>
        `;
    }

    /**
     * 创建会议室属性面板
     */
    createMeetingRoomProperties() {
        const roomId = this.selectedObject.roomId || this.selectedObject.get('roomId');
        const room = this.dataManager.getMeetingRoomByObjectId(roomId);
        
        return `
            <div class="property-group">
                <label>会议室名称</label>
                <input type="text" id="roomName" value="${room ? room.name : '会议室'}" placeholder="输入会议室名称">
            </div>
            <div class="property-group">
                <label>容纳人数</label>
                <input type="number" id="roomCapacity" value="${room ? room.capacity : 0}" min="0">
            </div>
            <div class="property-group">
                <label>设备信息</label>
                <textarea id="roomEquipment" placeholder="输入设备信息">${room ? room.equipment.join(', ') : ''}</textarea>
            </div>
            <button class="btn btn-primary" onclick="canvasManager.updateRoomInfo()">更新会议室信息</button>
        `;
    }

    /**
     * 创建文本属性面板
     */
    createTextProperties() {
        return `
            <div class="property-group">
                <label>文本内容</label>
                <textarea id="textContent">${this.selectedObject.text || ''}</textarea>
            </div>
            <div class="property-group">
                <label>字体大小</label>
                <input type="number" id="fontSize" value="${this.selectedObject.fontSize || 16}" min="8" max="72">
            </div>
            <div class="property-group">
                <label>文本颜色</label>
                <input type="color" id="textColor" value="${this.selectedObject.fill || '#333333'}">
            </div>
            <button class="btn btn-primary" onclick="canvasManager.updateTextProperties()">更新文本属性</button>
        `;
    }

    /**
     * 创建通用属性面板
     */
    createGeneralProperties() {
        return `
            <div class="property-group">
                <label>X坐标</label>
                <input type="number" id="objectX" value="${Math.round(this.selectedObject.left || 0)}">
            </div>
            <div class="property-group">
                <label>Y坐标</label>
                <input type="number" id="objectY" value="${Math.round(this.selectedObject.top || 0)}">
            </div>
            <div class="property-group">
                <label>旋转角度</label>
                <input type="number" id="objectAngle" value="${Math.round(this.selectedObject.angle || 0)}" min="0" max="360">
            </div>
            <button class="btn btn-primary" onclick="canvasManager.updateGeneralProperties()">更新属性</button>
            <button class="btn btn-danger" onclick="canvasManager.deleteSelectedObject()">删除对象</button>
        `;
    }

    /**
     * 清空属性面板
     */
    clearPropertiesPanel() {
        const panel = document.getElementById('propertiesPanel');
        panel.innerHTML = '<p class="no-selection">请选择一个对象来编辑属性</p>';
    }

    /**
     * 绑定属性面板事件
     */
    bindPropertyEvents() {
        // 这里可以添加实时更新的事件监听器
    }

    /**
     * 更新座位信息
     */
    updateSeatInfo() {
        const name = document.getElementById('employeeName').value;
        const employeeId = document.getElementById('employeeId').value;
        const department = document.getElementById('department').value;
        const seatId = this.selectedObject.seatId || this.selectedObject.get('seatId');

        if (name && employeeId) {
            // 添加或更新员工信息
            const employeeData = {
                name: name,
                employeeId: employeeId,
                department: department,
                seatId: seatId
            };

            this.dataManager.addEmployee(employeeData);
            
            // 更新座位显示
            this.updateSeatDisplay(this.selectedObject, name);
            this.dataManager.showNotification('座位信息更新成功！', 'success');
        } else {
            this.dataManager.showNotification('请填写员工姓名和工号', 'warning');
        }
    }

    /**
     * 更新座位显示
     */
    updateSeatDisplay(seatObject, employeeName) {
        // 更新座位颜色表示已占用
        if (seatObject.type === 'group') {
            const rect = seatObject.getObjects()[0];
            const text = seatObject.getObjects()[1];
            
            rect.set({
                fill: '#ffebee',
                stroke: '#d32f2f'
            });
            
            text.set({
                text: employeeName
            });
        }
        
        this.canvas.renderAll();
    }

    /**
     * 更新会议室信息
     */
    updateRoomInfo() {
        const name = document.getElementById('roomName').value;
        const capacity = parseInt(document.getElementById('roomCapacity').value) || 0;
        const equipment = document.getElementById('roomEquipment').value.split(',').map(item => item.trim()).filter(item => item);
        const roomId = this.selectedObject.roomId || this.selectedObject.get('roomId');

        const roomData = {
            name: name,
            capacity: capacity,
            equipment: equipment,
            objectId: roomId
        };

        this.dataManager.addMeetingRoom(roomData);
        
        // 更新会议室显示
        if (this.selectedObject.type === 'group') {
            const text = this.selectedObject.getObjects()[1];
            text.set({ text: name });
        }
        
        this.canvas.renderAll();
        this.dataManager.showNotification('会议室信息更新成功！', 'success');
    }

    /**
     * 更新文本属性
     */
    updateTextProperties() {
        const content = document.getElementById('textContent').value;
        const fontSize = parseInt(document.getElementById('fontSize').value) || 16;
        const color = document.getElementById('textColor').value;

        this.selectedObject.set({
            text: content,
            fontSize: fontSize,
            fill: color
        });

        this.canvas.renderAll();
        this.dataManager.showNotification('文本属性更新成功！', 'success');
    }

    /**
     * 更新通用属性
     */
    updateGeneralProperties() {
        const x = parseInt(document.getElementById('objectX').value) || 0;
        const y = parseInt(document.getElementById('objectY').value) || 0;
        const angle = parseInt(document.getElementById('objectAngle').value) || 0;

        this.selectedObject.set({
            left: x,
            top: y,
            angle: angle
        });

        this.selectedObject.setCoords();
        this.canvas.renderAll();
        this.dataManager.showNotification('属性更新成功！', 'success');
    }

    /**
     * 删除选中对象
     */
    deleteSelectedObject() {
        if (this.selectedObject) {
            this.canvas.remove(this.selectedObject);
            this.selectedObject = null;
            this.clearPropertiesPanel();
            this.dataManager.showNotification('对象已删除', 'info');
        }
    }

    /**
     * 缩放控制
     */
    zoomIn() {
        this.zoomLevel = Math.min(this.zoomLevel * 1.2, 3);
        this.canvas.setZoom(this.zoomLevel);
        this.updateZoomDisplay();
    }

    zoomOut() {
        this.zoomLevel = Math.max(this.zoomLevel / 1.2, 0.3);
        this.canvas.setZoom(this.zoomLevel);
        this.updateZoomDisplay();
    }

    resetZoom() {
        this.zoomLevel = 1;
        this.canvas.setZoom(1);
        this.canvas.viewportTransform = [1, 0, 0, 1, 0, 0];
        this.canvas.renderAll();
        this.updateZoomDisplay();
    }

    updateZoomDisplay() {
        document.getElementById('zoomLevel').textContent = Math.round(this.zoomLevel * 100) + '%';
    }

    /**
     * 保存画布状态
     */
    saveCanvasState() {
        // 这里可以实现撤销/重做功能的状态保存
    }

    /**
     * 清空画布
     */
    clearCanvas() {
        this.canvas.clear();
        this.drawGrid();
        this.selectedObject = null;
        this.clearPropertiesPanel();
    }

    /**
     * 获取画布数据
     */
    getCanvasData() {
        return this.canvas.toJSON(['objectType', 'seatId', 'employeeId', 'roomId', 'roomName']);
    }

    /**
     * 加载画布数据
     */
    loadCanvasData(data) {
        this.canvas.loadFromJSON(data, () => {
            this.canvas.renderAll();
            this.drawGrid();
        });
    }

    /**
     * 高亮显示对象
     */
    highlightObject(objectId, objectType) {
        const objects = this.canvas.getObjects();
        let targetObject = null;

        // 查找目标对象
        objects.forEach(obj => {
            if (objectType === 'seat' && obj.seatId === objectId) {
                targetObject = obj;
            } else if (objectType === 'meeting-room' && obj.roomId === objectId) {
                targetObject = obj;
            }
        });

        if (targetObject) {
            // 清除之前的选择
            this.canvas.discardActiveObject();
            
            // 选中并居中显示目标对象
            this.canvas.setActiveObject(targetObject);
            this.canvas.centerObject(targetObject);
            this.canvas.renderAll();
            
            // 添加高亮效果
            targetObject.set({
                shadow: new fabric.Shadow({
                    color: '#ffc107',
                    blur: 20,
                    offsetX: 0,
                    offsetY: 0
                })
            });
            
            this.canvas.renderAll();
            
            // 3秒后移除高亮效果
            setTimeout(() => {
                targetObject.set({ shadow: null });
                this.canvas.renderAll();
            }, 3000);
        }
    }
}
