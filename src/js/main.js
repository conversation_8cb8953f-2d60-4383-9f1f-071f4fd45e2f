/**
 * 主应用文件 - 初始化应用并协调各个模块
 */

// 全局变量
let dataManager;
let canvasManager;
let searchManager;

/**
 * 应用初始化
 */
document.addEventListener('DOMContentLoaded', function() {
    initializeApplication();
});

/**
 * 初始化应用程序
 */
function initializeApplication() {
    try {
        // 初始化数据管理器
        dataManager = new DataManager();
        
        // 初始化画布管理器
        canvasManager = new CanvasManager('officeCanvas', dataManager);
        
        // 初始化搜索管理器
        searchManager = new SearchManager(dataManager, canvasManager);
        
        // 绑定UI事件
        bindUIEvents();
        
        // 加载示例数据
        loadSampleData();
        
        console.log('办公区域管理系统初始化完成');
        dataManager.showNotification('系统初始化完成！', 'success');
        
    } catch (error) {
        console.error('应用初始化失败:', error);
        alert('应用初始化失败，请刷新页面重试');
    }
}

/**
 * 绑定UI事件
 */
function bindUIEvents() {
    // 工具按钮事件
    bindToolButtons();

    // 顶部工具栏事件
    bindTopToolbarEvents();

    // 画布控制事件
    bindCanvasControlEvents();

    // 批量操作事件
    bindBatchOperationEvents();

    // 键盘快捷键
    bindKeyboardShortcuts();
}

/**
 * 绑定工具按钮事件
 */
function bindToolButtons() {
    const toolButtons = document.querySelectorAll('.tool-btn');
    
    toolButtons.forEach(button => {
        button.addEventListener('click', function() {
            const tool = this.dataset.tool;
            
            // 更新按钮状态
            toolButtons.forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');
            
            // 设置画布工具
            canvasManager.setTool(tool);
            
            // 显示工具提示
            dataManager.showNotification(`已选择 ${this.querySelector('.tool-label').textContent} 工具`, 'info');
        });
    });

    // 添加取消选择工具的功能
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            toolButtons.forEach(btn => btn.classList.remove('active'));
            canvasManager.setTool(null);
            dataManager.showNotification('已取消工具选择', 'info');
        }
    });
}

/**
 * 绑定批量操作事件
 */
function bindBatchOperationEvents() {
    // 批量座位按钮
    document.getElementById('batchSeatBtn').addEventListener('click', function() {
        canvasManager.showBatchSeatDialog();
        this.style.transform = 'scale(0.95)';
        setTimeout(() => {
            this.style.transform = 'scale(1)';
        }, 150);
    });

    // 对齐按钮
    document.getElementById('alignBtn').addEventListener('click', function() {
        const activeObjects = canvasManager.canvas.getActiveObjects();
        if (activeObjects.length < 2) {
            dataManager.showNotification('请选择至少2个对象进行对齐', 'warning');
            return;
        }
        canvasManager.showAlignToolbar();
        this.style.transform = 'scale(0.95)';
        setTimeout(() => {
            this.style.transform = 'scale(1)';
        }, 150);
    });

    // 选择模式按钮
    document.getElementById('selectModeBtn').addEventListener('click', function() {
        canvasManager.toggleSelectionMode();
        this.style.transform = 'scale(0.95)';
        setTimeout(() => {
            this.style.transform = 'scale(1)';
        }, 150);
    });
}

/**
 * 绑定顶部工具栏事件
 */
function bindTopToolbarEvents() {
    // 保存布局
    document.getElementById('saveBtn').addEventListener('click', function() {
        const canvasData = canvasManager.getCanvasData();
        if (dataManager.saveLayout(canvasData)) {
            this.style.transform = 'scale(0.95)';
            setTimeout(() => {
                this.style.transform = 'scale(1)';
            }, 150);
        }
    });

    // 加载布局
    document.getElementById('loadBtn').addEventListener('click', function() {
        const canvasData = dataManager.loadLayout();
        if (canvasData) {
            canvasManager.loadCanvasData(canvasData);
            this.style.transform = 'scale(0.95)';
            setTimeout(() => {
                this.style.transform = 'scale(1)';
            }, 150);
        }
    });

    // 清空画布
    document.getElementById('clearBtn').addEventListener('click', function() {
        if (confirm('确定要清空画布吗？此操作不可撤销。')) {
            canvasManager.clearCanvas();
            dataManager.showNotification('画布已清空', 'info');
        }
    });
}

/**
 * 绑定画布控制事件
 */
function bindCanvasControlEvents() {
    // 缩放控制
    document.getElementById('zoomInBtn').addEventListener('click', function() {
        canvasManager.zoomIn();
        this.style.transform = 'scale(0.9)';
        setTimeout(() => {
            this.style.transform = 'scale(1)';
        }, 150);
    });

    document.getElementById('zoomOutBtn').addEventListener('click', function() {
        canvasManager.zoomOut();
        this.style.transform = 'scale(0.9)';
        setTimeout(() => {
            this.style.transform = 'scale(1)';
        }, 150);
    });

    document.getElementById('resetZoomBtn').addEventListener('click', function() {
        canvasManager.resetZoom();
        this.style.transform = 'scale(0.9)';
        setTimeout(() => {
            this.style.transform = 'scale(1)';
        }, 150);
    });

    // 鼠标滚轮缩放
    const canvasWrapper = document.querySelector('.canvas-wrapper');
    canvasWrapper.addEventListener('wheel', function(e) {
        if (e.ctrlKey || e.metaKey) {
            e.preventDefault();
            
            if (e.deltaY < 0) {
                canvasManager.zoomIn();
            } else {
                canvasManager.zoomOut();
            }
        }
    });
}

/**
 * 绑定键盘快捷键
 */
function bindKeyboardShortcuts() {
    document.addEventListener('keydown', function(e) {
        // Ctrl/Cmd + S: 保存
        if ((e.ctrlKey || e.metaKey) && e.key === 's') {
            e.preventDefault();
            document.getElementById('saveBtn').click();
        }
        
        // Ctrl/Cmd + O: 加载
        if ((e.ctrlKey || e.metaKey) && e.key === 'o') {
            e.preventDefault();
            document.getElementById('loadBtn').click();
        }
        
        // Delete: 删除选中对象
        if (e.key === 'Delete' && canvasManager.selectedObject) {
            canvasManager.deleteSelectedObject();
        }
        
        // Ctrl/Cmd + Z: 撤销 (预留)
        if ((e.ctrlKey || e.metaKey) && e.key === 'z') {
            e.preventDefault();
            // TODO: 实现撤销功能
            dataManager.showNotification('撤销功能开发中...', 'info');
        }
        
        // Ctrl/Cmd + F: 搜索
        if ((e.ctrlKey || e.metaKey) && e.key === 'f') {
            e.preventDefault();
            document.getElementById('searchInput').focus();
        }

        // Ctrl/Cmd + A: 全选
        if ((e.ctrlKey || e.metaKey) && e.key === 'a') {
            e.preventDefault();
            const objects = canvasManager.canvas.getObjects().filter(obj =>
                obj.objectType !== 'grid-line' && obj.objectType !== 'alignment-guide'
            );
            if (objects.length > 0) {
                const selection = new fabric.ActiveSelection(objects, {
                    canvas: canvasManager.canvas
                });
                canvasManager.canvas.setActiveObject(selection);
                canvasManager.canvas.renderAll();
                dataManager.showNotification(`已选择 ${objects.length} 个对象`, 'success');
            }
        }

        // Ctrl/Cmd + D: 复制选中对象
        if ((e.ctrlKey || e.metaKey) && e.key === 'd') {
            e.preventDefault();
            const activeObject = canvasManager.canvas.getActiveObject();
            if (activeObject) {
                canvasManager.copyObject(activeObject);
                canvasManager.pasteObject({ x: activeObject.left + 20, y: activeObject.top + 20 });
            }
        }

        // B: 批量座位
        if (e.key === 'b' && !e.ctrlKey && !e.metaKey && !e.altKey) {
            document.getElementById('batchSeatBtn').click();
        }

        // G: 对齐工具
        if (e.key === 'g' && !e.ctrlKey && !e.metaKey && !e.altKey) {
            document.getElementById('alignBtn').click();
        }

        // V: 选择模式
        if (e.key === 'v' && !e.ctrlKey && !e.metaKey && !e.altKey) {
            document.getElementById('selectModeBtn').click();
        }

        // 数字键快速选择工具
        const toolMap = {
            '1': 'seat',
            '2': 'meeting-room',
            '3': 'reception',
            '4': 'wall',
            '5': 'door',
            '6': 'text'
        };

        if (toolMap[e.key] && !e.ctrlKey && !e.metaKey && !e.altKey) {
            const toolBtn = document.querySelector(`[data-tool="${toolMap[e.key]}"]`);
            if (toolBtn) {
                toolBtn.click();
            }
        }
    });
}

/**
 * 加载示例数据
 */
function loadSampleData() {
    // 添加示例员工数据
    const sampleEmployees = [
        {
            name: '张三',
            employeeId: 'EMP001',
            department: '技术部',
            position: '前端工程师',
            phone: '13800138001',
            email: '<EMAIL>'
        },
        {
            name: '李四',
            employeeId: 'EMP002',
            department: '技术部',
            position: '后端工程师',
            phone: '13800138002',
            email: '<EMAIL>'
        },
        {
            name: '王五',
            employeeId: 'EMP003',
            department: '产品部',
            position: '产品经理',
            phone: '13800138003',
            email: '<EMAIL>'
        },
        {
            name: '赵六',
            employeeId: 'EMP004',
            department: '设计部',
            position: 'UI设计师',
            phone: '13800138004',
            email: '<EMAIL>'
        }
    ];

    sampleEmployees.forEach(emp => {
        dataManager.addEmployee(emp);
    });

    // 添加示例会议室数据
    const sampleMeetingRooms = [
        {
            name: '会议室A',
            capacity: 8,
            equipment: ['投影仪', '白板', '视频会议设备'],
            location: '2楼东侧'
        },
        {
            name: '会议室B',
            capacity: 12,
            equipment: ['投影仪', '音响设备'],
            location: '2楼西侧'
        },
        {
            name: '小会议室',
            capacity: 4,
            equipment: ['白板'],
            location: '3楼北侧'
        }
    ];

    sampleMeetingRooms.forEach(room => {
        dataManager.addMeetingRoom(room);
    });

    console.log('示例数据加载完成');
}

/**
 * 显示应用统计信息
 */
function showStatistics() {
    const stats = dataManager.getStatistics();
    
    const modalBody = document.getElementById('modalBody');
    modalBody.innerHTML = `
        <h3>办公区域统计</h3>
        <div class="statistics-grid">
            <div class="stat-item">
                <div class="stat-number">${stats.totalEmployees}</div>
                <div class="stat-label">总员工数</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">${stats.totalMeetingRooms}</div>
                <div class="stat-label">会议室数量</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">${stats.totalSeats}</div>
                <div class="stat-label">总座位数</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">${stats.occupiedSeats}</div>
                <div class="stat-label">已占用座位</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">${stats.availableSeats}</div>
                <div class="stat-label">可用座位</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">${stats.occupancyRate}%</div>
                <div class="stat-label">座位使用率</div>
            </div>
        </div>
        <div class="modal-actions">
            <button class="btn btn-primary" onclick="dataManager.exportData()">导出数据</button>
            <button class="btn btn-secondary" onclick="searchManager.closeModal()">关闭</button>
        </div>
    `;
    
    searchManager.showModal();
}

/**
 * 显示帮助信息
 */
function showHelp() {
    const modalBody = document.getElementById('modalBody');
    modalBody.innerHTML = `
        <h3>使用帮助</h3>
        <div class="help-content">
            <div class="help-section">
                <h4>绘图工具</h4>
                <ul>
                    <li><strong>座位 (1)</strong>: 点击画布创建座位，可分配给员工</li>
                    <li><strong>会议室 (2)</strong>: 点击画布创建会议室</li>
                    <li><strong>前台 (3)</strong>: 点击画布创建前台区域</li>
                    <li><strong>墙体 (4)</strong>: 拖拽创建墙体线条</li>
                    <li><strong>门 (5)</strong>: 点击画布创建门</li>
                    <li><strong>文本 (6)</strong>: 点击画布添加文本标记</li>
                </ul>
            </div>

            <div class="help-section">
                <h4>批量操作</h4>
                <ul>
                    <li><strong>批量座位 (B)</strong>: 输入如"5*10"批量创建座位网格</li>
                    <li><strong>对齐工具 (G)</strong>: 对选中的多个对象进行对齐</li>
                    <li><strong>选择模式 (V)</strong>: 启用多选和框选功能</li>
                    <li><strong>右键菜单</strong>: 右键点击对象或空白区域显示操作菜单</li>
                </ul>
            </div>
            
            <div class="help-section">
                <h4>快捷键</h4>
                <ul>
                    <li><strong>Ctrl+S</strong>: 保存布局</li>
                    <li><strong>Ctrl+O</strong>: 加载布局</li>
                    <li><strong>Ctrl+F</strong>: 搜索</li>
                    <li><strong>Ctrl+A</strong>: 全选对象</li>
                    <li><strong>Ctrl+D</strong>: 复制选中对象</li>
                    <li><strong>Delete</strong>: 删除选中对象</li>
                    <li><strong>Escape</strong>: 取消工具选择</li>
                    <li><strong>1-6</strong>: 快速选择绘图工具</li>
                    <li><strong>B</strong>: 批量添加座位</li>
                    <li><strong>G</strong>: 对齐工具</li>
                    <li><strong>V</strong>: 选择模式</li>
                </ul>
            </div>
            
            <div class="help-section">
                <h4>搜索功能</h4>
                <ul>
                    <li>输入员工姓名或工号搜索员工位置</li>
                    <li>输入会议室名称搜索会议室位置</li>
                    <li>点击搜索结果可定位到对应位置</li>
                    <li>支持实时搜索和高级搜索</li>
                </ul>
            </div>
            
            <div class="help-section">
                <h4>画布操作</h4>
                <ul>
                    <li>拖拽对象移动位置（自动对齐到附近对象）</li>
                    <li>选中对象后拖拽角点调整大小</li>
                    <li>双击文本对象可直接编辑</li>
                    <li>Ctrl+滚轮缩放画布</li>
                    <li>右侧属性面板可编辑对象属性</li>
                    <li>右键点击显示上下文菜单</li>
                    <li>拖拽选择多个对象进行批量操作</li>
                    <li>移动对象时显示红色对齐辅助线</li>
                </ul>
            </div>
        </div>
        <div class="modal-actions">
            <button class="btn btn-secondary" onclick="searchManager.closeModal()">关闭</button>
        </div>
    `;
    
    searchManager.showModal();
}

/**
 * 窗口大小改变时调整画布
 */
window.addEventListener('resize', function() {
    // 延迟执行以避免频繁调整
    clearTimeout(window.resizeTimeout);
    window.resizeTimeout = setTimeout(function() {
        if (canvasManager && canvasManager.canvas) {
            canvasManager.canvas.calcOffset();
        }
    }, 250);
});

/**
 * 页面卸载前保存数据
 */
window.addEventListener('beforeunload', function(e) {
    if (canvasManager) {
        const canvasData = canvasManager.getCanvasData();
        dataManager.saveLayout(canvasData);
    }
});

// 添加一些实用的全局函数供HTML调用
window.showStatistics = showStatistics;
window.showHelp = showHelp;

// 导出主要对象供其他脚本使用
window.dataManager = dataManager;
window.canvasManager = canvasManager;
window.searchManager = searchManager;
