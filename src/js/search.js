/**
 * 搜索功能模块 - 负责员工和会议室的搜索功能
 */
class SearchManager {
    constructor(dataManager, canvasManager) {
        this.dataManager = dataManager;
        this.canvasManager = canvasManager;
        this.searchInput = document.getElementById('searchInput');
        this.searchBtn = document.getElementById('searchBtn');
        this.searchResults = document.getElementById('searchResults');
        
        this.initializeSearch();
    }

    /**
     * 初始化搜索功能
     */
    initializeSearch() {
        // 搜索按钮点击事件
        this.searchBtn.addEventListener('click', () => {
            this.performSearch();
        });

        // 搜索输入框回车事件
        this.searchInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.performSearch();
            }
        });

        // 实时搜索（输入时延迟搜索）
        let searchTimeout;
        this.searchInput.addEventListener('input', () => {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                if (this.searchInput.value.trim().length >= 2) {
                    this.performSearch();
                } else if (this.searchInput.value.trim().length === 0) {
                    this.clearSearchResults();
                }
            }, 300);
        });
    }

    /**
     * 执行搜索
     */
    performSearch() {
        const query = this.searchInput.value.trim();
        
        if (!query) {
            this.clearSearchResults();
            return;
        }

        // 搜索员工和会议室
        const employees = this.dataManager.searchEmployees(query);
        const meetingRooms = this.dataManager.searchMeetingRooms(query);

        this.displaySearchResults(employees, meetingRooms, query);
    }

    /**
     * 显示搜索结果
     */
    displaySearchResults(employees, meetingRooms, query) {
        let html = '';

        if (employees.length === 0 && meetingRooms.length === 0) {
            html = `
                <div class="search-no-results">
                    <p>未找到与 "${query}" 相关的结果</p>
                    <small>请尝试其他关键词</small>
                </div>
            `;
        } else {
            // 员工搜索结果
            if (employees.length > 0) {
                html += '<div class="search-section"><h4>员工</h4>';
                employees.forEach(employee => {
                    html += this.createEmployeeResultItem(employee);
                });
                html += '</div>';
            }

            // 会议室搜索结果
            if (meetingRooms.length > 0) {
                html += '<div class="search-section"><h4>会议室</h4>';
                meetingRooms.forEach(room => {
                    html += this.createMeetingRoomResultItem(room);
                });
                html += '</div>';
            }
        }

        this.searchResults.innerHTML = html;
        this.bindSearchResultEvents();
    }

    /**
     * 创建员工搜索结果项
     */
    createEmployeeResultItem(employee) {
        const seatInfo = employee.seatId ? `座位: ${employee.seatId}` : '未分配座位';
        const departmentInfo = employee.department ? ` | ${employee.department}` : '';
        
        return `
            <div class="search-result-item" data-type="employee" data-id="${employee.id}" data-seat-id="${employee.seatId}">
                <div class="result-title">${this.highlightQuery(employee.name, this.searchInput.value)}</div>
                <div class="result-info">
                    工号: ${this.highlightQuery(employee.employeeId, this.searchInput.value)} | ${seatInfo}${departmentInfo}
                </div>
                ${employee.position ? `<div class="result-position">${employee.position}</div>` : ''}
            </div>
        `;
    }

    /**
     * 创建会议室搜索结果项
     */
    createMeetingRoomResultItem(room) {
        const capacityInfo = room.capacity > 0 ? `容纳 ${room.capacity} 人` : '';
        const locationInfo = room.location ? ` | ${room.location}` : '';
        const equipmentInfo = room.equipment && room.equipment.length > 0 ? 
            `<div class="result-equipment">设备: ${room.equipment.join(', ')}</div>` : '';
        
        return `
            <div class="search-result-item" data-type="meeting-room" data-id="${room.id}" data-object-id="${room.objectId}">
                <div class="result-title">${this.highlightQuery(room.name, this.searchInput.value)}</div>
                <div class="result-info">
                    ${capacityInfo}${locationInfo}
                </div>
                ${equipmentInfo}
            </div>
        `;
    }

    /**
     * 高亮搜索关键词
     */
    highlightQuery(text, query) {
        if (!query) return text;
        
        const regex = new RegExp(`(${query})`, 'gi');
        return text.replace(regex, '<mark>$1</mark>');
    }

    /**
     * 绑定搜索结果事件
     */
    bindSearchResultEvents() {
        const resultItems = this.searchResults.querySelectorAll('.search-result-item');
        
        resultItems.forEach(item => {
            item.addEventListener('click', () => {
                this.handleResultClick(item);
            });

            // 添加悬停效果
            item.addEventListener('mouseenter', () => {
                item.style.transform = 'translateX(4px)';
            });

            item.addEventListener('mouseleave', () => {
                item.style.transform = 'translateX(0)';
            });
        });
    }

    /**
     * 处理搜索结果点击
     */
    handleResultClick(item) {
        const type = item.dataset.type;
        const id = item.dataset.id;

        if (type === 'employee') {
            this.handleEmployeeClick(item, id);
        } else if (type === 'meeting-room') {
            this.handleMeetingRoomClick(item, id);
        }
    }

    /**
     * 处理员工点击
     */
    handleEmployeeClick(item, employeeId) {
        const seatId = item.dataset.seatId;
        
        if (seatId && seatId !== 'null') {
            // 在画布中高亮显示员工座位
            this.canvasManager.highlightObject(seatId, 'seat');
            this.dataManager.showNotification('已定位到员工座位', 'success');
        } else {
            this.dataManager.showNotification('该员工尚未分配座位', 'info');
        }

        // 显示员工详细信息
        this.showEmployeeDetails(employeeId);
    }

    /**
     * 处理会议室点击
     */
    handleMeetingRoomClick(item, roomId) {
        const objectId = item.dataset.objectId;
        
        if (objectId && objectId !== 'null') {
            // 在画布中高亮显示会议室
            this.canvasManager.highlightObject(objectId, 'meeting-room');
            this.dataManager.showNotification('已定位到会议室', 'success');
        } else {
            this.dataManager.showNotification('会议室位置信息不完整', 'warning');
        }

        // 显示会议室详细信息
        this.showMeetingRoomDetails(roomId);
    }

    /**
     * 显示员工详细信息
     */
    showEmployeeDetails(employeeId) {
        const employee = this.dataManager.employees.find(emp => emp.id === employeeId);
        if (!employee) return;

        const modalBody = document.getElementById('modalBody');
        modalBody.innerHTML = `
            <h3>员工信息</h3>
            <div class="employee-details">
                <div class="detail-row">
                    <label>姓名:</label>
                    <span>${employee.name}</span>
                </div>
                <div class="detail-row">
                    <label>工号:</label>
                    <span>${employee.employeeId}</span>
                </div>
                <div class="detail-row">
                    <label>部门:</label>
                    <span>${employee.department || '未设置'}</span>
                </div>
                <div class="detail-row">
                    <label>职位:</label>
                    <span>${employee.position || '未设置'}</span>
                </div>
                <div class="detail-row">
                    <label>座位:</label>
                    <span>${employee.seatId || '未分配'}</span>
                </div>
                <div class="detail-row">
                    <label>电话:</label>
                    <span>${employee.phone || '未设置'}</span>
                </div>
                <div class="detail-row">
                    <label>邮箱:</label>
                    <span>${employee.email || '未设置'}</span>
                </div>
            </div>
            <div class="modal-actions">
                <button class="btn btn-primary" onclick="searchManager.editEmployee('${employee.id}')">编辑信息</button>
                <button class="btn btn-secondary" onclick="searchManager.closeModal()">关闭</button>
            </div>
        `;

        this.showModal();
    }

    /**
     * 显示会议室详细信息
     */
    showMeetingRoomDetails(roomId) {
        const room = this.dataManager.meetingRooms.find(r => r.id === roomId);
        if (!room) return;

        const modalBody = document.getElementById('modalBody');
        modalBody.innerHTML = `
            <h3>会议室信息</h3>
            <div class="room-details">
                <div class="detail-row">
                    <label>名称:</label>
                    <span>${room.name}</span>
                </div>
                <div class="detail-row">
                    <label>容纳人数:</label>
                    <span>${room.capacity || 0} 人</span>
                </div>
                <div class="detail-row">
                    <label>位置:</label>
                    <span>${room.location || '未设置'}</span>
                </div>
                <div class="detail-row">
                    <label>设备:</label>
                    <span>${room.equipment && room.equipment.length > 0 ? room.equipment.join(', ') : '无'}</span>
                </div>
            </div>
            <div class="modal-actions">
                <button class="btn btn-primary" onclick="searchManager.editMeetingRoom('${room.id}')">编辑信息</button>
                <button class="btn btn-secondary" onclick="searchManager.closeModal()">关闭</button>
            </div>
        `;

        this.showModal();
    }

    /**
     * 编辑员工信息
     */
    editEmployee(employeeId) {
        const employee = this.dataManager.employees.find(emp => emp.id === employeeId);
        if (!employee) return;

        const modalBody = document.getElementById('modalBody');
        modalBody.innerHTML = `
            <h3>编辑员工信息</h3>
            <form id="editEmployeeForm">
                <div class="form-group">
                    <label>姓名:</label>
                    <input type="text" id="editName" value="${employee.name}" required>
                </div>
                <div class="form-group">
                    <label>工号:</label>
                    <input type="text" id="editEmployeeId" value="${employee.employeeId}" required>
                </div>
                <div class="form-group">
                    <label>部门:</label>
                    <input type="text" id="editDepartment" value="${employee.department || ''}">
                </div>
                <div class="form-group">
                    <label>职位:</label>
                    <input type="text" id="editPosition" value="${employee.position || ''}">
                </div>
                <div class="form-group">
                    <label>电话:</label>
                    <input type="text" id="editPhone" value="${employee.phone || ''}">
                </div>
                <div class="form-group">
                    <label>邮箱:</label>
                    <input type="email" id="editEmail" value="${employee.email || ''}">
                </div>
            </form>
            <div class="modal-actions">
                <button class="btn btn-primary" onclick="searchManager.saveEmployeeChanges('${employee.id}')">保存</button>
                <button class="btn btn-secondary" onclick="searchManager.closeModal()">取消</button>
            </div>
        `;
    }

    /**
     * 保存员工信息更改
     */
    saveEmployeeChanges(employeeId) {
        const updateData = {
            name: document.getElementById('editName').value,
            employeeId: document.getElementById('editEmployeeId').value,
            department: document.getElementById('editDepartment').value,
            position: document.getElementById('editPosition').value,
            phone: document.getElementById('editPhone').value,
            email: document.getElementById('editEmail').value
        };

        if (this.dataManager.updateEmployee(employeeId, updateData)) {
            this.dataManager.showNotification('员工信息更新成功！', 'success');
            this.closeModal();
            // 重新执行搜索以更新结果
            this.performSearch();
        } else {
            this.dataManager.showNotification('更新失败，请重试', 'error');
        }
    }

    /**
     * 编辑会议室信息
     */
    editMeetingRoom(roomId) {
        const room = this.dataManager.meetingRooms.find(r => r.id === roomId);
        if (!room) return;

        const modalBody = document.getElementById('modalBody');
        modalBody.innerHTML = `
            <h3>编辑会议室信息</h3>
            <form id="editRoomForm">
                <div class="form-group">
                    <label>名称:</label>
                    <input type="text" id="editRoomName" value="${room.name}" required>
                </div>
                <div class="form-group">
                    <label>容纳人数:</label>
                    <input type="number" id="editRoomCapacity" value="${room.capacity || 0}" min="0">
                </div>
                <div class="form-group">
                    <label>位置:</label>
                    <input type="text" id="editRoomLocation" value="${room.location || ''}">
                </div>
                <div class="form-group">
                    <label>设备 (用逗号分隔):</label>
                    <textarea id="editRoomEquipment">${room.equipment ? room.equipment.join(', ') : ''}</textarea>
                </div>
            </form>
            <div class="modal-actions">
                <button class="btn btn-primary" onclick="searchManager.saveMeetingRoomChanges('${room.id}')">保存</button>
                <button class="btn btn-secondary" onclick="searchManager.closeModal()">取消</button>
            </div>
        `;
    }

    /**
     * 保存会议室信息更改
     */
    saveMeetingRoomChanges(roomId) {
        const updateData = {
            name: document.getElementById('editRoomName').value,
            capacity: parseInt(document.getElementById('editRoomCapacity').value) || 0,
            location: document.getElementById('editRoomLocation').value,
            equipment: document.getElementById('editRoomEquipment').value
                .split(',')
                .map(item => item.trim())
                .filter(item => item)
        };

        if (this.dataManager.updateMeetingRoom(roomId, updateData)) {
            this.dataManager.showNotification('会议室信息更新成功！', 'success');
            this.closeModal();
            // 重新执行搜索以更新结果
            this.performSearch();
        } else {
            this.dataManager.showNotification('更新失败，请重试', 'error');
        }
    }

    /**
     * 显示模态对话框
     */
    showModal() {
        const modal = document.getElementById('modal');
        modal.style.display = 'block';
        
        // 点击关闭按钮
        const closeBtn = modal.querySelector('.close');
        closeBtn.onclick = () => this.closeModal();
        
        // 点击模态背景关闭
        modal.onclick = (e) => {
            if (e.target === modal) {
                this.closeModal();
            }
        };
    }

    /**
     * 关闭模态对话框
     */
    closeModal() {
        const modal = document.getElementById('modal');
        modal.style.display = 'none';
    }

    /**
     * 清空搜索结果
     */
    clearSearchResults() {
        this.searchResults.innerHTML = '';
    }

    /**
     * 获取搜索建议
     */
    getSearchSuggestions(query) {
        const suggestions = [];
        
        // 员工姓名建议
        this.dataManager.employees.forEach(emp => {
            if (emp.name.toLowerCase().includes(query.toLowerCase())) {
                suggestions.push({
                    type: 'employee',
                    text: emp.name,
                    subtitle: `工号: ${emp.employeeId}`
                });
            }
        });

        // 会议室名称建议
        this.dataManager.meetingRooms.forEach(room => {
            if (room.name.toLowerCase().includes(query.toLowerCase())) {
                suggestions.push({
                    type: 'meeting-room',
                    text: room.name,
                    subtitle: room.location || '会议室'
                });
            }
        });

        return suggestions.slice(0, 5); // 限制建议数量
    }

    /**
     * 高级搜索
     */
    showAdvancedSearch() {
        const modalBody = document.getElementById('modalBody');
        modalBody.innerHTML = `
            <h3>高级搜索</h3>
            <form id="advancedSearchForm">
                <div class="search-tabs">
                    <button type="button" class="tab-btn active" data-tab="employee">员工搜索</button>
                    <button type="button" class="tab-btn" data-tab="room">会议室搜索</button>
                </div>
                
                <div id="employeeSearchTab" class="search-tab-content active">
                    <div class="form-group">
                        <label>姓名:</label>
                        <input type="text" id="advSearchName">
                    </div>
                    <div class="form-group">
                        <label>工号:</label>
                        <input type="text" id="advSearchEmployeeId">
                    </div>
                    <div class="form-group">
                        <label>部门:</label>
                        <input type="text" id="advSearchDepartment">
                    </div>
                    <div class="form-group">
                        <label>座位状态:</label>
                        <select id="advSearchSeatStatus">
                            <option value="">全部</option>
                            <option value="assigned">已分配</option>
                            <option value="unassigned">未分配</option>
                        </select>
                    </div>
                </div>
                
                <div id="roomSearchTab" class="search-tab-content">
                    <div class="form-group">
                        <label>会议室名称:</label>
                        <input type="text" id="advSearchRoomName">
                    </div>
                    <div class="form-group">
                        <label>最小容纳人数:</label>
                        <input type="number" id="advSearchMinCapacity" min="0">
                    </div>
                    <div class="form-group">
                        <label>设备要求:</label>
                        <input type="text" id="advSearchEquipment" placeholder="例如：投影仪, 白板">
                    </div>
                </div>
            </form>
            <div class="modal-actions">
                <button class="btn btn-primary" onclick="searchManager.performAdvancedSearch()">搜索</button>
                <button class="btn btn-secondary" onclick="searchManager.closeModal()">取消</button>
            </div>
        `;

        this.showModal();
        this.bindAdvancedSearchEvents();
    }

    /**
     * 绑定高级搜索事件
     */
    bindAdvancedSearchEvents() {
        const tabBtns = document.querySelectorAll('.tab-btn');
        const tabContents = document.querySelectorAll('.search-tab-content');

        tabBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                const tabId = btn.dataset.tab;
                
                // 更新按钮状态
                tabBtns.forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
                
                // 更新内容显示
                tabContents.forEach(content => {
                    content.classList.remove('active');
                });
                document.getElementById(tabId + 'SearchTab').classList.add('active');
            });
        });
    }

    /**
     * 执行高级搜索
     */
    performAdvancedSearch() {
        const activeTab = document.querySelector('.tab-btn.active').dataset.tab;
        
        if (activeTab === 'employee') {
            this.performAdvancedEmployeeSearch();
        } else {
            this.performAdvancedRoomSearch();
        }
        
        this.closeModal();
    }

    /**
     * 执行高级员工搜索
     */
    performAdvancedEmployeeSearch() {
        const criteria = {
            name: document.getElementById('advSearchName').value,
            employeeId: document.getElementById('advSearchEmployeeId').value,
            department: document.getElementById('advSearchDepartment').value,
            seatStatus: document.getElementById('advSearchSeatStatus').value
        };

        let results = this.dataManager.employees.filter(emp => {
            let match = true;
            
            if (criteria.name && !emp.name.toLowerCase().includes(criteria.name.toLowerCase())) {
                match = false;
            }
            if (criteria.employeeId && !emp.employeeId.toLowerCase().includes(criteria.employeeId.toLowerCase())) {
                match = false;
            }
            if (criteria.department && !emp.department.toLowerCase().includes(criteria.department.toLowerCase())) {
                match = false;
            }
            if (criteria.seatStatus === 'assigned' && !emp.seatId) {
                match = false;
            }
            if (criteria.seatStatus === 'unassigned' && emp.seatId) {
                match = false;
            }
            
            return match;
        });

        this.displaySearchResults(results, [], '高级搜索');
    }

    /**
     * 执行高级会议室搜索
     */
    performAdvancedRoomSearch() {
        const criteria = {
            name: document.getElementById('advSearchRoomName').value,
            minCapacity: parseInt(document.getElementById('advSearchMinCapacity').value) || 0,
            equipment: document.getElementById('advSearchEquipment').value
        };

        let results = this.dataManager.meetingRooms.filter(room => {
            let match = true;
            
            if (criteria.name && !room.name.toLowerCase().includes(criteria.name.toLowerCase())) {
                match = false;
            }
            if (criteria.minCapacity > 0 && room.capacity < criteria.minCapacity) {
                match = false;
            }
            if (criteria.equipment) {
                const requiredEquipment = criteria.equipment.toLowerCase().split(',').map(e => e.trim());
                const roomEquipment = room.equipment.map(e => e.toLowerCase());
                const hasAllEquipment = requiredEquipment.every(req => 
                    roomEquipment.some(eq => eq.includes(req))
                );
                if (!hasAllEquipment) {
                    match = false;
                }
            }
            
            return match;
        });

        this.displaySearchResults([], results, '高级搜索');
    }
}
