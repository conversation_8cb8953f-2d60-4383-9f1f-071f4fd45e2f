#!/bin/bash

# 办公区域管理系统启动脚本

echo "🏢 启动办公区域管理系统..."
echo "📁 项目目录: $(pwd)"

# 检查是否存在src目录
if [ ! -d "src" ]; then
    echo "❌ 错误: 找不到src目录，请确保在项目根目录下运行此脚本"
    exit 1
fi

# 检查Python是否安装
if command -v python3 &> /dev/null; then
    PYTHON_CMD="python3"
elif command -v python &> /dev/null; then
    PYTHON_CMD="python"
else
    echo "❌ 错误: 未找到Python，请先安装Python"
    exit 1
fi

echo "🐍 使用Python: $PYTHON_CMD"

# 启动HTTP服务器
echo "🚀 启动HTTP服务器..."
echo "📡 服务地址: http://localhost:8000"
echo "🔗 请在浏览器中打开上述地址"
echo "⏹️  按 Ctrl+C 停止服务器"
echo ""

cd src && $PYTHON_CMD -m http.server 8000
