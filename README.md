# 办公区域管理系统

一个基于Web的办公区域管理系统，支持CAD式绘图功能，可以绘制座位表、会议室、前台等办公区域元素，并提供员工座位查询和会议室搜索功能。

## 功能特性

### 🎨 CAD式绘图功能
- **座位绘制**: 创建座位并分配给员工
- **会议室绘制**: 创建会议室并设置容量、设备信息
- **前台绘制**: 创建前台区域
- **墙体绘制**: 拖拽创建墙体线条
- **门窗绘制**: 添加门窗元素
- **文本标记**: 添加文本注释和标签

### 🔍 智能搜索功能
- **员工搜索**: 通过姓名或工号快速查找员工座位
- **会议室搜索**: 通过名称搜索会议室位置
- **实时搜索**: 输入时自动显示搜索结果
- **高级搜索**: 支持多条件组合搜索
- **位置定位**: 点击搜索结果自动定位到画布位置

### 📊 数据管理功能
- **本地存储**: 自动保存布局和数据到浏览器本地存储
- **数据导出**: 支持导出JSON格式的数据文件
- **数据导入**: 支持从JSON文件导入数据
- **统计信息**: 显示员工数量、座位使用率等统计数据

### 🎛️ 画布操作功能
- **缩放控制**: 支持画布缩放和重置
- **对象编辑**: 拖拽移动、调整大小、旋转对象
- **属性编辑**: 右侧面板编辑选中对象的属性
- **快捷键支持**: 丰富的键盘快捷键操作

## 技术栈

- **前端框架**: 原生HTML5 + CSS3 + JavaScript
- **绘图库**: Fabric.js - 强大的HTML5 Canvas库
- **数据存储**: localStorage - 浏览器本地存储
- **UI设计**: 现代化响应式设计，支持移动端

## 项目结构

```
src/
├── index.html              # 主页面
├── css/
│   ├── main.css           # 主样式文件
│   └── canvas.css         # 画布相关样式
├── js/
│   ├── main.js            # 主应用逻辑
│   ├── canvas-manager.js  # 画布管理器
│   ├── data-manager.js    # 数据管理器
│   └── search.js          # 搜索功能
├── lib/
│   └── fabric.min.js      # Fabric.js库
└── assets/
    └── icons/             # 图标资源
```

## 快速开始

### 1. 启动项目

由于这是一个纯前端项目，您可以通过以下方式之一启动：

**方法一：直接打开HTML文件**
```bash
# 在浏览器中直接打开
open src/index.html
```

**方法二：使用本地服务器（推荐）**
```bash
# 使用Python启动简单HTTP服务器
cd src
python -m http.server 8000

# 或使用Node.js的http-server
npx http-server src -p 8000
```

然后在浏览器中访问 `http://localhost:8000`

### 2. 基本使用

1. **选择绘图工具**: 点击左侧工具栏中的工具按钮
2. **绘制元素**: 在画布上点击或拖拽创建元素
3. **编辑属性**: 选中对象后在右侧属性面板编辑信息
4. **搜索功能**: 在左侧搜索框中输入员工姓名或会议室名称
5. **保存布局**: 点击顶部"保存布局"按钮保存当前设计

## 使用指南

### 绘图工具使用

| 工具 | 快捷键 | 说明 |
|------|--------|------|
| 座位 | 1 | 点击画布创建座位，可在属性面板分配员工 |
| 会议室 | 2 | 点击画布创建会议室，可设置容量和设备 |
| 前台 | 3 | 点击画布创建前台区域 |
| 墙体 | 4 | 拖拽创建墙体线条 |
| 门 | 5 | 点击画布创建门 |
| 文本 | 6 | 点击画布添加文本标记 |

### 快捷键

| 快捷键 | 功能 |
|--------|------|
| Ctrl+S | 保存布局 |
| Ctrl+O | 加载布局 |
| Ctrl+F | 搜索 |
| Delete | 删除选中对象 |
| Escape | 取消工具选择 |
| 1-6 | 快速选择绘图工具 |
| Ctrl+滚轮 | 缩放画布 |

### 搜索功能

1. **员工搜索**: 输入员工姓名或工号，系统会显示匹配的员工信息
2. **会议室搜索**: 输入会议室名称，系统会显示会议室信息
3. **点击定位**: 点击搜索结果可在画布中高亮显示对应位置
4. **高级搜索**: 支持按部门、座位状态等条件筛选

## 数据管理

### 自动保存
系统会自动将布局数据保存到浏览器的localStorage中，下次打开时会自动加载。

### 手动保存/加载
- 点击"保存布局"按钮手动保存当前状态
- 点击"加载布局"按钮恢复之前保存的状态

### 数据导出/导入
- 在统计信息页面可以导出JSON格式的数据文件
- 支持从JSON文件导入员工和会议室数据

## 浏览器兼容性

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## 开发说明

### 核心类说明

1. **DataManager**: 数据管理器，负责员工、会议室数据的增删改查
2. **CanvasManager**: 画布管理器，负责Fabric.js画布的操作和绘图功能
3. **SearchManager**: 搜索管理器，负责搜索功能和结果展示

### 扩展开发

如需添加新的绘图工具或功能，可以：

1. 在`CanvasManager`中添加新的绘图方法
2. 在`main.js`中添加对应的工具按钮事件
3. 在CSS中添加相应的样式

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request来改进这个项目！

## 更新日志

### v1.0.0 (2024-06-29)
- 初始版本发布
- 支持基本的CAD式绘图功能
- 实现员工和会议室搜索功能
- 添加数据管理和本地存储功能
- 响应式设计支持移动端使用
