@echo off
chcp 65001 >nul

echo 🏢 启动办公区域管理系统...
echo 📁 项目目录: %cd%

REM 检查是否存在src目录
if not exist "src" (
    echo ❌ 错误: 找不到src目录，请确保在项目根目录下运行此脚本
    pause
    exit /b 1
)

REM 检查Python是否安装
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误: 未找到Python，请先安装Python
    pause
    exit /b 1
)

echo 🐍 使用Python: python
echo 🚀 启动HTTP服务器...
echo 📡 服务地址: http://localhost:8000
echo 🔗 请在浏览器中打开上述地址
echo ⏹️  按 Ctrl+C 停止服务器
echo.

cd src
python -m http.server 8000

pause
